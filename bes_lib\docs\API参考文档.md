# BES蓝牙连接框架 API参考文档

## 核心类API

### SppConnector

SPP连接器，负责管理蓝牙连接的核心类。

#### 静态方法

##### getConnector()
```java
public static SppConnector getConnector()
```
获取SppConnector的单例实例。

**返回值**：
- `SppConnector` - 连接器实例

**示例**：
```java
SppConnector connector = SppConnector.getConnector();
```

#### 实例方法

##### connect(String address)
```java
public boolean connect(String address)
```
通过设备地址建立蓝牙连接。

**参数**：
- `address` - 蓝牙设备MAC地址，格式如"00:11:22:33:44:55"

**返回值**：
- `boolean` - true表示开始连接过程，false表示当前状态不允许连接

**示例**：
```java
boolean result = connector.connect("00:11:22:33:44:55");
```

##### connect(BluetoothDevice device)
```java
public boolean connect(@NonNull BluetoothDevice device)
```
通过BluetoothDevice对象建立蓝牙连接。

**参数**：
- `device` - BluetoothDevice对象，不能为null

**返回值**：
- `boolean` - true表示开始连接过程，false表示当前状态不允许连接

**示例**：
```java
BluetoothDevice device = adapter.getRemoteDevice(address);
boolean result = connector.connect(device);
```

##### disconnect()
```java
public void disconnect()
```
断开当前蓝牙连接。

**示例**：
```java
connector.disconnect();
```

##### write(byte[] data)
```java
public boolean write(byte[] data)
```
向连接的设备发送数据。

**参数**：
- `data` - 要发送的字节数组

**返回值**：
- `boolean` - true表示发送成功，false表示发送失败

**示例**：
```java
byte[] data = {0x01, 0x02, 0x03};
boolean success = connector.write(data);
```

##### isConnected()
```java
public boolean isConnected()
```
检查当前连接状态。

**返回值**：
- `boolean` - true表示已连接，false表示未连接

**示例**：
```java
if (connector.isConnected()) {
    // 执行需要连接的操作
}
```

##### addConnectCallback(ConnectCallback callback)
```java
public void addConnectCallback(ConnectCallback callback)
```
添加连接状态回调。

**参数**：
- `callback` - 连接回调接口实现

**示例**：
```java
connector.addConnectCallback(new ConnectCallback() {
    @Override
    public void onConnectionStateChanged(boolean connected) {
        // 处理连接状态变化
    }
    
    @Override
    public void onReceive(UUID uuid, byte[] data) {
        // 处理接收到的数据
    }
});
```

##### removeConnectCallback(ConnectCallback callback)
```java
public void removeConnectCallback(ConnectCallback callback)
```
移除指定的连接回调。

**参数**：
- `callback` - 要移除的回调接口

##### removeAllCallBack()
```java
public void removeAllCallBack()
```
移除所有连接回调。

**示例**：
```java
connector.removeAllCallBack();
```

#### 常量

##### sUUID
```java
public static final UUID sUUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")
```
SPP服务的标准UUID。

---

### BtHelper

蓝牙工具类，提供蓝牙相关的辅助方法。

#### 静态方法

##### getBluetoothManager(Context context)
```java
public static BluetoothManager getBluetoothManager(Context context)
```
获取蓝牙管理器。

**参数**：
- `context` - Android上下文

**返回值**：
- `BluetoothManager` - 蓝牙管理器实例

##### getBluetoothAdapter(Context context)
```java
public static BluetoothAdapter getBluetoothAdapter(Context context)
```
获取蓝牙适配器，兼容不同Android版本。

**参数**：
- `context` - Android上下文

**返回值**：
- `BluetoothAdapter` - 蓝牙适配器实例

##### getRemoteDevice(Context context, String address)
```java
public static BluetoothDevice getRemoteDevice(Context context, String address)
```
根据地址获取远程蓝牙设备。

**参数**：
- `context` - Android上下文
- `address` - 设备MAC地址

**返回值**：
- `BluetoothDevice` - 蓝牙设备对象，获取失败返回null

##### createBond(BluetoothDevice btDevice)
```java
public static boolean createBond(BluetoothDevice btDevice)
```
与指定设备建立配对。

**参数**：
- `btDevice` - 要配对的蓝牙设备

**返回值**：
- `boolean` - true表示配对成功，false表示配对失败

##### removeBond(BluetoothDevice btDevice)
```java
public static boolean removeBond(BluetoothDevice btDevice)
```
移除与指定设备的配对。

**参数**：
- `btDevice` - 要解除配对的蓝牙设备

**返回值**：
- `boolean` - true表示解除配对成功，false表示失败

##### parseManufacturerSpecificData(byte[] scanRecord)
```java
public static byte[] parseManufacturerSpecificData(byte[] scanRecord)
```
解析蓝牙广播数据中的厂商特定数据。

**参数**：
- `scanRecord` - 蓝牙扫描记录

**返回值**：
- `byte[]` - 厂商特定数据，解析失败返回null

---

### ClassicScanner

经典蓝牙设备扫描器。

#### 构造方法

##### ClassicScanner(Context context)
```java
public ClassicScanner(Context context)
```
创建经典蓝牙扫描器实例。

**参数**：
- `context` - Android上下文

#### 实例方法

##### startScan(ScanCallback callback)
```java
public void startScan(ScanCallback callback)
```
开始扫描蓝牙设备。

**参数**：
- `callback` - 扫描结果回调

**示例**：
```java
ClassicScanner scanner = new ClassicScanner(context);
scanner.startScan(new ScanCallback() {
    @Override
    public void onScanStart() {
        // 扫描开始
    }
    
    @Override
    public void onFound(BluetoothDevice device, int rssi, byte[] scanRecord) {
        // 发现设备
    }
    
    @Override
    public void onScanFinish() {
        // 扫描结束
    }
});
```

##### stopScan()
```java
public void stopScan()
```
停止设备扫描。

##### close()
```java
public void close()
```
关闭扫描器，释放资源。

---

### SppMessageHelper

SPP消息处理辅助类。

#### 静态方法

##### getInstant()
```java
public static SppMessageHelper getInstant()
```
获取SppMessageHelper的单例实例。

**返回值**：
- `SppMessageHelper` - 消息处理器实例

#### 实例方法

##### checkDataEnoughAndRetArray(byte[] data)
```java
synchronized public byte[][] checkDataEnoughAndRetArray(byte[] data)
```
检查数据是否满足协议要求并返回完整的数据包数组。

**参数**：
- `data` - 接收到的原始数据

**返回值**：
- `byte[][]` - 完整的数据包数组，如果数据不完整返回null

##### clearSppData()
```java
public void clearSppData()
```
清理缓存的SPP数据。

**示例**：
```java
SppMessageHelper helper = SppMessageHelper.getInstant();
helper.clearSppData();
```

---

## 回调接口

### ConnectCallback

连接状态和数据接收回调接口。

#### 方法

##### onConnectionStateChanged(boolean connected)
```java
void onConnectionStateChanged(boolean connected)
```
连接状态变化回调。

**参数**：
- `connected` - true表示已连接，false表示已断开

##### onReceive(UUID uuid, byte[] data)
```java
void onReceive(UUID uuid, byte[] data)
```
数据接收回调。

**参数**：
- `uuid` - 服务UUID（当前实现中为null）
- `data` - 接收到的数据

---

### ScanCallback

设备扫描回调接口。

#### 方法

##### onScanStart()
```java
void onScanStart()
```
扫描开始回调。

##### onFound(BluetoothDevice device, int rssi, byte[] scanRecord)
```java
void onFound(BluetoothDevice device, int rssi, byte[] scanRecord)
```
发现设备回调。

**参数**：
- `device` - 发现的蓝牙设备
- `rssi` - 信号强度
- `scanRecord` - 扫描记录（经典蓝牙为null）

##### onScanFinish()
```java
void onScanFinish()
```
扫描结束回调。

---

## 常量定义

### 连接状态
```java
private static final int STATE_CONNECTING = 1;    // 连接中
private static final int STATE_CONNECTED = 2;     // 已连接  
private static final int STATE_DISCONNECTED = 0;  // 已断开
```

### SPP UUID
```java
public static final UUID sUUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
```

### 数据包类型
- `0xFF 0xFF` - 音频数据流标识
- `0x07` - 控制指令（5字节）
- `0x04` - 开始数据流标志（5字节）
- `0x05` - 结束数据流标志（4字节）
- `0x01` - 数据流开始标志（12字节）
- `0x03` - 数据流结束标志（16字节）
- `0x00` - 可变长度指令

---

## 异常处理

### IOException
在以下情况下可能抛出：
- 蓝牙连接失败
- 数据读写异常
- Socket关闭异常

### SecurityException
在以下情况下可能抛出：
- 缺少蓝牙权限
- 反射调用受限方法失败

### IllegalArgumentException
在以下情况下可能抛出：
- 无效的设备地址
- 参数格式错误

---

## 使用注意事项

1. **权限要求**：确保应用具有必要的蓝牙权限
2. **线程安全**：回调方法可能在非UI线程中执行
3. **资源管理**：及时释放连接和扫描资源
4. **状态检查**：在操作前检查连接状态
5. **异常处理**：妥善处理各种异常情况
