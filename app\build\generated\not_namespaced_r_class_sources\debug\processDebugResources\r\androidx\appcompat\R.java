/* AUTO-GENERATED FILE.  DO NOT MODIFY.
 *
 * This class was automatically generated by the
 * gradle plugin from the resource data it found. It
 * should not be modified by hand.
 */
package androidx.appcompat;

public final class R {
    private R() {}

    public static final class anim {
        private anim() {}

        public static final int abc_fade_in = 0x7f010000;
        public static final int abc_fade_out = 0x7f010001;
        public static final int abc_grow_fade_in_from_bottom = 0x7f010002;
        public static final int abc_popup_enter = 0x7f010003;
        public static final int abc_popup_exit = 0x7f010004;
        public static final int abc_shrink_fade_out_from_bottom = 0x7f010005;
        public static final int abc_slide_in_bottom = 0x7f010006;
        public static final int abc_slide_in_top = 0x7f010007;
        public static final int abc_slide_out_bottom = 0x7f010008;
        public static final int abc_slide_out_top = 0x7f010009;
        public static final int abc_tooltip_enter = 0x7f01000a;
        public static final int abc_tooltip_exit = 0x7f01000b;
        public static final int btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c;
        public static final int btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d;
        public static final int btn_checkbox_to_checked_icon_null_animation = 0x7f01000e;
        public static final int btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f;
        public static final int btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010;
        public static final int btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011;
        public static final int btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012;
        public static final int btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013;
        public static final int btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014;
        public static final int btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015;
        public static final int btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016;
        public static final int btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017;
    }
    public static final class attr {
        private attr() {}

        public static final int actionBarDivider = 0x7f030000;
        public static final int actionBarItemBackground = 0x7f030001;
        public static final int actionBarPopupTheme = 0x7f030002;
        public static final int actionBarSize = 0x7f030003;
        public static final int actionBarSplitStyle = 0x7f030004;
        public static final int actionBarStyle = 0x7f030005;
        public static final int actionBarTabBarStyle = 0x7f030006;
        public static final int actionBarTabStyle = 0x7f030007;
        public static final int actionBarTabTextStyle = 0x7f030008;
        public static final int actionBarTheme = 0x7f030009;
        public static final int actionBarWidgetTheme = 0x7f03000a;
        public static final int actionButtonStyle = 0x7f03000b;
        public static final int actionDropDownStyle = 0x7f03000c;
        public static final int actionLayout = 0x7f03000d;
        public static final int actionMenuTextAppearance = 0x7f03000e;
        public static final int actionMenuTextColor = 0x7f03000f;
        public static final int actionModeBackground = 0x7f030010;
        public static final int actionModeCloseButtonStyle = 0x7f030011;
        public static final int actionModeCloseDrawable = 0x7f030012;
        public static final int actionModeCopyDrawable = 0x7f030013;
        public static final int actionModeCutDrawable = 0x7f030014;
        public static final int actionModeFindDrawable = 0x7f030015;
        public static final int actionModePasteDrawable = 0x7f030016;
        public static final int actionModePopupWindowStyle = 0x7f030017;
        public static final int actionModeSelectAllDrawable = 0x7f030018;
        public static final int actionModeShareDrawable = 0x7f030019;
        public static final int actionModeSplitBackground = 0x7f03001a;
        public static final int actionModeStyle = 0x7f03001b;
        public static final int actionModeWebSearchDrawable = 0x7f03001c;
        public static final int actionOverflowButtonStyle = 0x7f03001d;
        public static final int actionOverflowMenuStyle = 0x7f03001e;
        public static final int actionProviderClass = 0x7f03001f;
        public static final int actionViewClass = 0x7f030021;
        public static final int activityChooserViewStyle = 0x7f030022;
        public static final int alertDialogButtonGroupStyle = 0x7f030023;
        public static final int alertDialogCenterButtons = 0x7f030024;
        public static final int alertDialogStyle = 0x7f030025;
        public static final int alertDialogTheme = 0x7f030026;
        public static final int allowStacking = 0x7f03002a;
        public static final int alpha = 0x7f03002b;
        public static final int alphabeticModifiers = 0x7f03002c;
        public static final int arrowHeadLength = 0x7f03002e;
        public static final int arrowShaftLength = 0x7f03002f;
        public static final int autoCompleteTextViewStyle = 0x7f030030;
        public static final int autoSizeMaxTextSize = 0x7f030031;
        public static final int autoSizeMinTextSize = 0x7f030032;
        public static final int autoSizePresetSizes = 0x7f030033;
        public static final int autoSizeStepGranularity = 0x7f030034;
        public static final int autoSizeTextType = 0x7f030035;
        public static final int background = 0x7f030036;
        public static final int backgroundSplit = 0x7f03003c;
        public static final int backgroundStacked = 0x7f03003d;
        public static final int backgroundTint = 0x7f03003e;
        public static final int backgroundTintMode = 0x7f03003f;
        public static final int barLength = 0x7f030040;
        public static final int borderlessButtonStyle = 0x7f030048;
        public static final int buttonBarButtonStyle = 0x7f030056;
        public static final int buttonBarNegativeButtonStyle = 0x7f030057;
        public static final int buttonBarNeutralButtonStyle = 0x7f030058;
        public static final int buttonBarPositiveButtonStyle = 0x7f030059;
        public static final int buttonBarStyle = 0x7f03005a;
        public static final int buttonCompat = 0x7f03005b;
        public static final int buttonGravity = 0x7f03005c;
        public static final int buttonIconDimen = 0x7f03005d;
        public static final int buttonPanelSideLayout = 0x7f03005e;
        public static final int buttonStyle = 0x7f03005f;
        public static final int buttonStyleSmall = 0x7f030060;
        public static final int buttonTint = 0x7f030061;
        public static final int buttonTintMode = 0x7f030062;
        public static final int checkboxStyle = 0x7f03006a;
        public static final int checkedTextViewStyle = 0x7f03006f;
        public static final int closeIcon = 0x7f030084;
        public static final int closeItemLayout = 0x7f03008b;
        public static final int collapseContentDescription = 0x7f03008c;
        public static final int collapseIcon = 0x7f03008d;
        public static final int color = 0x7f030090;
        public static final int colorAccent = 0x7f030091;
        public static final int colorBackgroundFloating = 0x7f030092;
        public static final int colorButtonNormal = 0x7f030093;
        public static final int colorControlActivated = 0x7f030094;
        public static final int colorControlHighlight = 0x7f030095;
        public static final int colorControlNormal = 0x7f030096;
        public static final int colorError = 0x7f030097;
        public static final int colorPrimary = 0x7f0300a3;
        public static final int colorPrimaryDark = 0x7f0300a4;
        public static final int colorSwitchThumbNormal = 0x7f0300a9;
        public static final int commitIcon = 0x7f0300aa;
        public static final int contentDescription = 0x7f0300ab;
        public static final int contentInsetEnd = 0x7f0300ac;
        public static final int contentInsetEndWithActions = 0x7f0300ad;
        public static final int contentInsetLeft = 0x7f0300ae;
        public static final int contentInsetRight = 0x7f0300af;
        public static final int contentInsetStart = 0x7f0300b0;
        public static final int contentInsetStartWithNavigation = 0x7f0300b1;
        public static final int controlBackground = 0x7f0300b8;
        public static final int coordinatorLayoutStyle = 0x7f0300b9;
        public static final int customNavigationLayout = 0x7f0300cb;
        public static final int defaultQueryHint = 0x7f0300cc;
        public static final int dialogCornerRadius = 0x7f0300cd;
        public static final int dialogPreferredPadding = 0x7f0300ce;
        public static final int dialogTheme = 0x7f0300cf;
        public static final int displayOptions = 0x7f0300d0;
        public static final int divider = 0x7f0300d1;
        public static final int dividerHorizontal = 0x7f0300d2;
        public static final int dividerPadding = 0x7f0300d3;
        public static final int dividerVertical = 0x7f0300d4;
        public static final int drawableBottomCompat = 0x7f0300d5;
        public static final int drawableEndCompat = 0x7f0300d6;
        public static final int drawableLeftCompat = 0x7f0300d7;
        public static final int drawableRightCompat = 0x7f0300d8;
        public static final int drawableSize = 0x7f0300d9;
        public static final int drawableStartCompat = 0x7f0300da;
        public static final int drawableTopCompat = 0x7f0300db;
        public static final int drawerArrowStyle = 0x7f0300dc;
        public static final int dropDownListViewStyle = 0x7f0300dd;
        public static final int dropdownListPreferredItemHeight = 0x7f0300de;
        public static final int editTextBackground = 0x7f0300df;
        public static final int editTextColor = 0x7f0300e0;
        public static final int editTextStyle = 0x7f0300e1;
        public static final int elevation = 0x7f0300e2;
        public static final int expandActivityOverflowButtonDrawable = 0x7f0300e9;
        public static final int firstBaselineToTopHeight = 0x7f0300fe;
        public static final int font = 0x7f030100;
        public static final int fontFamily = 0x7f030101;
        public static final int fontProviderAuthority = 0x7f030102;
        public static final int fontProviderCerts = 0x7f030103;
        public static final int fontProviderFetchStrategy = 0x7f030104;
        public static final int fontProviderFetchTimeout = 0x7f030105;
        public static final int fontProviderPackage = 0x7f030106;
        public static final int fontProviderQuery = 0x7f030107;
        public static final int fontStyle = 0x7f030108;
        public static final int fontVariationSettings = 0x7f030109;
        public static final int fontWeight = 0x7f03010a;
        public static final int gapBetweenBars = 0x7f03010c;
        public static final int goIcon = 0x7f03010d;
        public static final int height = 0x7f03010f;
        public static final int hideOnContentScroll = 0x7f030115;
        public static final int homeAsUpIndicator = 0x7f03011b;
        public static final int homeLayout = 0x7f03011c;
        public static final int icon = 0x7f03011e;
        public static final int iconTint = 0x7f030124;
        public static final int iconTintMode = 0x7f030125;
        public static final int iconifiedByDefault = 0x7f030126;
        public static final int imageButtonStyle = 0x7f030127;
        public static final int indeterminateProgressStyle = 0x7f030128;
        public static final int initialActivityCount = 0x7f030129;
        public static final int isLightTheme = 0x7f03012b;
        public static final int itemPadding = 0x7f030133;
        public static final int keylines = 0x7f030139;
        public static final int lastBaselineToBottomHeight = 0x7f03013b;
        public static final int layout = 0x7f03013c;
        public static final int layout_anchor = 0x7f03013e;
        public static final int layout_anchorGravity = 0x7f03013f;
        public static final int layout_behavior = 0x7f030140;
        public static final int layout_dodgeInsetEdges = 0x7f030143;
        public static final int layout_insetEdge = 0x7f030144;
        public static final int layout_keyline = 0x7f030145;
        public static final int lineHeight = 0x7f03014a;
        public static final int listChoiceBackgroundIndicator = 0x7f03014c;
        public static final int listChoiceIndicatorMultipleAnimated = 0x7f03014d;
        public static final int listChoiceIndicatorSingleAnimated = 0x7f03014e;
        public static final int listDividerAlertDialog = 0x7f03014f;
        public static final int listItemLayout = 0x7f030150;
        public static final int listLayout = 0x7f030151;
        public static final int listMenuViewStyle = 0x7f030152;
        public static final int listPopupWindowStyle = 0x7f030153;
        public static final int listPreferredItemHeight = 0x7f030154;
        public static final int listPreferredItemHeightLarge = 0x7f030155;
        public static final int listPreferredItemHeightSmall = 0x7f030156;
        public static final int listPreferredItemPaddingEnd = 0x7f030157;
        public static final int listPreferredItemPaddingLeft = 0x7f030158;
        public static final int listPreferredItemPaddingRight = 0x7f030159;
        public static final int listPreferredItemPaddingStart = 0x7f03015a;
        public static final int logo = 0x7f03015b;
        public static final int logoDescription = 0x7f03015c;
        public static final int maxButtonHeight = 0x7f030161;
        public static final int measureWithLargestChild = 0x7f030163;
        public static final int multiChoiceItemLayout = 0x7f030166;
        public static final int navigationContentDescription = 0x7f030167;
        public static final int navigationIcon = 0x7f030168;
        public static final int navigationMode = 0x7f030169;
        public static final int numericModifiers = 0x7f03016b;
        public static final int overlapAnchor = 0x7f03016c;
        public static final int paddingBottomNoButtons = 0x7f03016d;
        public static final int paddingEnd = 0x7f03016e;
        public static final int paddingStart = 0x7f03016f;
        public static final int paddingTopNoTitle = 0x7f030170;
        public static final int panelBackground = 0x7f030171;
        public static final int panelMenuListTheme = 0x7f030172;
        public static final int panelMenuListWidth = 0x7f030173;
        public static final int popupMenuStyle = 0x7f030179;
        public static final int popupTheme = 0x7f03017a;
        public static final int popupWindowStyle = 0x7f03017b;
        public static final int preserveIconSpacing = 0x7f03017c;
        public static final int progressBarPadding = 0x7f03017e;
        public static final int progressBarStyle = 0x7f03017f;
        public static final int queryBackground = 0x7f030180;
        public static final int queryHint = 0x7f030181;
        public static final int radioButtonStyle = 0x7f030182;
        public static final int ratingBarStyle = 0x7f030183;
        public static final int ratingBarStyleIndicator = 0x7f030184;
        public static final int ratingBarStyleSmall = 0x7f030185;
        public static final int searchHintIcon = 0x7f03018b;
        public static final int searchIcon = 0x7f03018c;
        public static final int searchViewStyle = 0x7f03018d;
        public static final int seekBarStyle = 0x7f03018e;
        public static final int selectableItemBackground = 0x7f03018f;
        public static final int selectableItemBackgroundBorderless = 0x7f030190;
        public static final int showAsAction = 0x7f030196;
        public static final int showDividers = 0x7f030197;
        public static final int showText = 0x7f030199;
        public static final int showTitle = 0x7f03019a;
        public static final int singleChoiceItemLayout = 0x7f03019b;
        public static final int spinBars = 0x7f0301a1;
        public static final int spinnerDropDownItemStyle = 0x7f0301a2;
        public static final int spinnerStyle = 0x7f0301a3;
        public static final int splitTrack = 0x7f0301a4;
        public static final int srcCompat = 0x7f0301a5;
        public static final int state_above_anchor = 0x7f0301a7;
        public static final int statusBarBackground = 0x7f0301ac;
        public static final int subMenuArrow = 0x7f0301b0;
        public static final int submitBackground = 0x7f0301b1;
        public static final int subtitle = 0x7f0301b2;
        public static final int subtitleTextAppearance = 0x7f0301b3;
        public static final int subtitleTextColor = 0x7f0301b4;
        public static final int subtitleTextStyle = 0x7f0301b5;
        public static final int suggestionRowLayout = 0x7f0301b6;
        public static final int switchMinWidth = 0x7f0301b7;
        public static final int switchPadding = 0x7f0301b8;
        public static final int switchStyle = 0x7f0301b9;
        public static final int switchTextAppearance = 0x7f0301ba;
        public static final int textAllCaps = 0x7f0301d5;
        public static final int textAppearanceLargePopupMenu = 0x7f0301e0;
        public static final int textAppearanceListItem = 0x7f0301e1;
        public static final int textAppearanceListItemSecondary = 0x7f0301e2;
        public static final int textAppearanceListItemSmall = 0x7f0301e3;
        public static final int textAppearancePopupMenuHeader = 0x7f0301e5;
        public static final int textAppearanceSearchResultSubtitle = 0x7f0301e6;
        public static final int textAppearanceSearchResultTitle = 0x7f0301e7;
        public static final int textAppearanceSmallPopupMenu = 0x7f0301e8;
        public static final int textColorAlertDialogListItem = 0x7f0301eb;
        public static final int textColorSearchUrl = 0x7f0301ec;
        public static final int textLocale = 0x7f0301ef;
        public static final int theme = 0x7f0301f1;
        public static final int thickness = 0x7f0301f2;
        public static final int thumbTextPadding = 0x7f0301f3;
        public static final int thumbTint = 0x7f0301f4;
        public static final int thumbTintMode = 0x7f0301f5;
        public static final int tickMark = 0x7f0301f6;
        public static final int tickMarkTint = 0x7f0301f7;
        public static final int tickMarkTintMode = 0x7f0301f8;
        public static final int tint = 0x7f0301f9;
        public static final int tintMode = 0x7f0301fa;
        public static final int title = 0x7f0301fb;
        public static final int titleMargin = 0x7f0301fd;
        public static final int titleMarginBottom = 0x7f0301fe;
        public static final int titleMarginEnd = 0x7f0301ff;
        public static final int titleMarginStart = 0x7f030200;
        public static final int titleMarginTop = 0x7f030201;
        public static final int titleMargins = 0x7f030202;
        public static final int titleTextAppearance = 0x7f030203;
        public static final int titleTextColor = 0x7f030204;
        public static final int titleTextStyle = 0x7f030205;
        public static final int toolbarNavigationButtonStyle = 0x7f030207;
        public static final int toolbarStyle = 0x7f030208;
        public static final int tooltipForegroundColor = 0x7f030209;
        public static final int tooltipFrameBackground = 0x7f03020a;
        public static final int tooltipText = 0x7f03020b;
        public static final int track = 0x7f03020c;
        public static final int trackTint = 0x7f03020d;
        public static final int trackTintMode = 0x7f03020e;
        public static final int ttcIndex = 0x7f03020f;
        public static final int viewInflaterClass = 0x7f030212;
        public static final int voiceIcon = 0x7f030213;
        public static final int windowActionBar = 0x7f030214;
        public static final int windowActionBarOverlay = 0x7f030215;
        public static final int windowActionModeOverlay = 0x7f030216;
        public static final int windowFixedHeightMajor = 0x7f030217;
        public static final int windowFixedHeightMinor = 0x7f030218;
        public static final int windowFixedWidthMajor = 0x7f030219;
        public static final int windowFixedWidthMinor = 0x7f03021a;
        public static final int windowMinWidthMajor = 0x7f03021b;
        public static final int windowMinWidthMinor = 0x7f03021c;
        public static final int windowNoTitle = 0x7f03021d;
    }
    public static final class bool {
        private bool() {}

        public static final int abc_action_bar_embed_tabs = 0x7f040000;
        public static final int abc_allow_stacked_button_bar = 0x7f040001;
        public static final int abc_config_actionMenuItemAllCaps = 0x7f040002;
    }
    public static final class color {
        private color() {}

        public static final int abc_background_cache_hint_selector_material_dark = 0x7f050000;
        public static final int abc_background_cache_hint_selector_material_light = 0x7f050001;
        public static final int abc_btn_colored_borderless_text_material = 0x7f050002;
        public static final int abc_btn_colored_text_material = 0x7f050003;
        public static final int abc_color_highlight_material = 0x7f050004;
        public static final int abc_hint_foreground_material_dark = 0x7f050005;
        public static final int abc_hint_foreground_material_light = 0x7f050006;
        public static final int abc_input_method_navigation_guard = 0x7f050007;
        public static final int abc_primary_text_disable_only_material_dark = 0x7f050008;
        public static final int abc_primary_text_disable_only_material_light = 0x7f050009;
        public static final int abc_primary_text_material_dark = 0x7f05000a;
        public static final int abc_primary_text_material_light = 0x7f05000b;
        public static final int abc_search_url_text = 0x7f05000c;
        public static final int abc_search_url_text_normal = 0x7f05000d;
        public static final int abc_search_url_text_pressed = 0x7f05000e;
        public static final int abc_search_url_text_selected = 0x7f05000f;
        public static final int abc_secondary_text_material_dark = 0x7f050010;
        public static final int abc_secondary_text_material_light = 0x7f050011;
        public static final int abc_tint_btn_checkable = 0x7f050012;
        public static final int abc_tint_default = 0x7f050013;
        public static final int abc_tint_edittext = 0x7f050014;
        public static final int abc_tint_seek_thumb = 0x7f050015;
        public static final int abc_tint_spinner = 0x7f050016;
        public static final int abc_tint_switch_track = 0x7f050017;
        public static final int accent_material_dark = 0x7f050018;
        public static final int accent_material_light = 0x7f050019;
        public static final int background_floating_material_dark = 0x7f05001a;
        public static final int background_floating_material_light = 0x7f05001b;
        public static final int background_material_dark = 0x7f05001c;
        public static final int background_material_light = 0x7f05001d;
        public static final int bright_foreground_disabled_material_dark = 0x7f05001f;
        public static final int bright_foreground_disabled_material_light = 0x7f050020;
        public static final int bright_foreground_inverse_material_dark = 0x7f050021;
        public static final int bright_foreground_inverse_material_light = 0x7f050022;
        public static final int bright_foreground_material_dark = 0x7f050023;
        public static final int bright_foreground_material_light = 0x7f050024;
        public static final int button_material_dark = 0x7f050025;
        public static final int button_material_light = 0x7f050026;
        public static final int dim_foreground_disabled_material_dark = 0x7f050058;
        public static final int dim_foreground_disabled_material_light = 0x7f050059;
        public static final int dim_foreground_material_dark = 0x7f05005a;
        public static final int dim_foreground_material_light = 0x7f05005b;
        public static final int error_color_material_dark = 0x7f05005c;
        public static final int error_color_material_light = 0x7f05005d;
        public static final int foreground_material_dark = 0x7f05005e;
        public static final int foreground_material_light = 0x7f05005f;
        public static final int highlighted_text_material_dark = 0x7f050061;
        public static final int highlighted_text_material_light = 0x7f050062;
        public static final int material_blue_grey_800 = 0x7f050063;
        public static final int material_blue_grey_900 = 0x7f050064;
        public static final int material_blue_grey_950 = 0x7f050065;
        public static final int material_deep_teal_200 = 0x7f050066;
        public static final int material_deep_teal_500 = 0x7f050067;
        public static final int material_grey_100 = 0x7f050068;
        public static final int material_grey_300 = 0x7f050069;
        public static final int material_grey_50 = 0x7f05006a;
        public static final int material_grey_600 = 0x7f05006b;
        public static final int material_grey_800 = 0x7f05006c;
        public static final int material_grey_850 = 0x7f05006d;
        public static final int material_grey_900 = 0x7f05006e;
        public static final int notification_action_color_filter = 0x7f05009a;
        public static final int notification_icon_bg_color = 0x7f05009b;
        public static final int primary_dark_material_dark = 0x7f05009c;
        public static final int primary_dark_material_light = 0x7f05009d;
        public static final int primary_material_dark = 0x7f05009e;
        public static final int primary_material_light = 0x7f05009f;
        public static final int primary_text_default_material_dark = 0x7f0500a0;
        public static final int primary_text_default_material_light = 0x7f0500a1;
        public static final int primary_text_disabled_material_dark = 0x7f0500a2;
        public static final int primary_text_disabled_material_light = 0x7f0500a3;
        public static final int ripple_material_dark = 0x7f0500a4;
        public static final int ripple_material_light = 0x7f0500a5;
        public static final int secondary_text_default_material_dark = 0x7f0500a6;
        public static final int secondary_text_default_material_light = 0x7f0500a7;
        public static final int secondary_text_disabled_material_dark = 0x7f0500a8;
        public static final int secondary_text_disabled_material_light = 0x7f0500a9;
        public static final int switch_thumb_disabled_material_dark = 0x7f0500aa;
        public static final int switch_thumb_disabled_material_light = 0x7f0500ab;
        public static final int switch_thumb_material_dark = 0x7f0500ac;
        public static final int switch_thumb_material_light = 0x7f0500ad;
        public static final int switch_thumb_normal_material_dark = 0x7f0500ae;
        public static final int switch_thumb_normal_material_light = 0x7f0500af;
        public static final int tooltip_background_dark = 0x7f0500b0;
        public static final int tooltip_background_light = 0x7f0500b1;
    }
    public static final class dimen {
        private dimen() {}

        public static final int abc_action_bar_content_inset_material = 0x7f060000;
        public static final int abc_action_bar_content_inset_with_nav = 0x7f060001;
        public static final int abc_action_bar_default_height_material = 0x7f060002;
        public static final int abc_action_bar_default_padding_end_material = 0x7f060003;
        public static final int abc_action_bar_default_padding_start_material = 0x7f060004;
        public static final int abc_action_bar_elevation_material = 0x7f060005;
        public static final int abc_action_bar_icon_vertical_padding_material = 0x7f060006;
        public static final int abc_action_bar_overflow_padding_end_material = 0x7f060007;
        public static final int abc_action_bar_overflow_padding_start_material = 0x7f060008;
        public static final int abc_action_bar_stacked_max_height = 0x7f060009;
        public static final int abc_action_bar_stacked_tab_max_width = 0x7f06000a;
        public static final int abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b;
        public static final int abc_action_bar_subtitle_top_margin_material = 0x7f06000c;
        public static final int abc_action_button_min_height_material = 0x7f06000d;
        public static final int abc_action_button_min_width_material = 0x7f06000e;
        public static final int abc_action_button_min_width_overflow_material = 0x7f06000f;
        public static final int abc_alert_dialog_button_bar_height = 0x7f060010;
        public static final int abc_alert_dialog_button_dimen = 0x7f060011;
        public static final int abc_button_inset_horizontal_material = 0x7f060012;
        public static final int abc_button_inset_vertical_material = 0x7f060013;
        public static final int abc_button_padding_horizontal_material = 0x7f060014;
        public static final int abc_button_padding_vertical_material = 0x7f060015;
        public static final int abc_cascading_menus_min_smallest_width = 0x7f060016;
        public static final int abc_config_prefDialogWidth = 0x7f060017;
        public static final int abc_control_corner_material = 0x7f060018;
        public static final int abc_control_inset_material = 0x7f060019;
        public static final int abc_control_padding_material = 0x7f06001a;
        public static final int abc_dialog_corner_radius_material = 0x7f06001b;
        public static final int abc_dialog_fixed_height_major = 0x7f06001c;
        public static final int abc_dialog_fixed_height_minor = 0x7f06001d;
        public static final int abc_dialog_fixed_width_major = 0x7f06001e;
        public static final int abc_dialog_fixed_width_minor = 0x7f06001f;
        public static final int abc_dialog_list_padding_bottom_no_buttons = 0x7f060020;
        public static final int abc_dialog_list_padding_top_no_title = 0x7f060021;
        public static final int abc_dialog_min_width_major = 0x7f060022;
        public static final int abc_dialog_min_width_minor = 0x7f060023;
        public static final int abc_dialog_padding_material = 0x7f060024;
        public static final int abc_dialog_padding_top_material = 0x7f060025;
        public static final int abc_dialog_title_divider_material = 0x7f060026;
        public static final int abc_disabled_alpha_material_dark = 0x7f060027;
        public static final int abc_disabled_alpha_material_light = 0x7f060028;
        public static final int abc_dropdownitem_icon_width = 0x7f060029;
        public static final int abc_dropdownitem_text_padding_left = 0x7f06002a;
        public static final int abc_dropdownitem_text_padding_right = 0x7f06002b;
        public static final int abc_edit_text_inset_bottom_material = 0x7f06002c;
        public static final int abc_edit_text_inset_horizontal_material = 0x7f06002d;
        public static final int abc_edit_text_inset_top_material = 0x7f06002e;
        public static final int abc_floating_window_z = 0x7f06002f;
        public static final int abc_list_item_padding_horizontal_material = 0x7f060030;
        public static final int abc_panel_menu_list_width = 0x7f060031;
        public static final int abc_progress_bar_height_material = 0x7f060032;
        public static final int abc_search_view_preferred_height = 0x7f060033;
        public static final int abc_search_view_preferred_width = 0x7f060034;
        public static final int abc_seekbar_track_background_height_material = 0x7f060035;
        public static final int abc_seekbar_track_progress_height_material = 0x7f060036;
        public static final int abc_select_dialog_padding_start_material = 0x7f060037;
        public static final int abc_switch_padding = 0x7f060038;
        public static final int abc_text_size_body_1_material = 0x7f060039;
        public static final int abc_text_size_body_2_material = 0x7f06003a;
        public static final int abc_text_size_button_material = 0x7f06003b;
        public static final int abc_text_size_caption_material = 0x7f06003c;
        public static final int abc_text_size_display_1_material = 0x7f06003d;
        public static final int abc_text_size_display_2_material = 0x7f06003e;
        public static final int abc_text_size_display_3_material = 0x7f06003f;
        public static final int abc_text_size_display_4_material = 0x7f060040;
        public static final int abc_text_size_headline_material = 0x7f060041;
        public static final int abc_text_size_large_material = 0x7f060042;
        public static final int abc_text_size_medium_material = 0x7f060043;
        public static final int abc_text_size_menu_header_material = 0x7f060044;
        public static final int abc_text_size_menu_material = 0x7f060045;
        public static final int abc_text_size_small_material = 0x7f060046;
        public static final int abc_text_size_subhead_material = 0x7f060047;
        public static final int abc_text_size_subtitle_material_toolbar = 0x7f060048;
        public static final int abc_text_size_title_material = 0x7f060049;
        public static final int abc_text_size_title_material_toolbar = 0x7f06004a;
        public static final int compat_button_inset_horizontal_material = 0x7f060050;
        public static final int compat_button_inset_vertical_material = 0x7f060051;
        public static final int compat_button_padding_horizontal_material = 0x7f060052;
        public static final int compat_button_padding_vertical_material = 0x7f060053;
        public static final int compat_control_corner_material = 0x7f060054;
        public static final int compat_notification_large_icon_max_height = 0x7f060055;
        public static final int compat_notification_large_icon_max_width = 0x7f060056;
        public static final int disabled_alpha_material_dark = 0x7f060085;
        public static final int disabled_alpha_material_light = 0x7f060086;
        public static final int highlight_alpha_material_colored = 0x7f06008b;
        public static final int highlight_alpha_material_dark = 0x7f06008c;
        public static final int highlight_alpha_material_light = 0x7f06008d;
        public static final int hint_alpha_material_dark = 0x7f06008e;
        public static final int hint_alpha_material_light = 0x7f06008f;
        public static final int hint_pressed_alpha_material_dark = 0x7f060090;
        public static final int hint_pressed_alpha_material_light = 0x7f060091;
        public static final int notification_action_icon_size = 0x7f0600d6;
        public static final int notification_action_text_size = 0x7f0600d7;
        public static final int notification_big_circle_margin = 0x7f0600d8;
        public static final int notification_content_margin_start = 0x7f0600d9;
        public static final int notification_large_icon_height = 0x7f0600da;
        public static final int notification_large_icon_width = 0x7f0600db;
        public static final int notification_main_column_padding_top = 0x7f0600dc;
        public static final int notification_media_narrow_margin = 0x7f0600dd;
        public static final int notification_right_icon_size = 0x7f0600de;
        public static final int notification_right_side_padding_top = 0x7f0600df;
        public static final int notification_small_icon_background_padding = 0x7f0600e0;
        public static final int notification_small_icon_size_as_large = 0x7f0600e1;
        public static final int notification_subtext_size = 0x7f0600e2;
        public static final int notification_top_pad = 0x7f0600e3;
        public static final int notification_top_pad_large_text = 0x7f0600e4;
        public static final int tooltip_corner_radius = 0x7f0600e6;
        public static final int tooltip_horizontal_padding = 0x7f0600e7;
        public static final int tooltip_margin = 0x7f0600e8;
        public static final int tooltip_precise_anchor_extra_offset = 0x7f0600e9;
        public static final int tooltip_precise_anchor_threshold = 0x7f0600ea;
        public static final int tooltip_vertical_padding = 0x7f0600eb;
        public static final int tooltip_y_offset_non_touch = 0x7f0600ec;
        public static final int tooltip_y_offset_touch = 0x7f0600ed;
    }
    public static final class drawable {
        private drawable() {}

        public static final int abc_ab_share_pack_mtrl_alpha = 0x7f070006;
        public static final int abc_action_bar_item_background_material = 0x7f070007;
        public static final int abc_btn_borderless_material = 0x7f070008;
        public static final int abc_btn_check_material = 0x7f070009;
        public static final int abc_btn_check_material_anim = 0x7f07000a;
        public static final int abc_btn_check_to_on_mtrl_000 = 0x7f07000b;
        public static final int abc_btn_check_to_on_mtrl_015 = 0x7f07000c;
        public static final int abc_btn_colored_material = 0x7f07000d;
        public static final int abc_btn_default_mtrl_shape = 0x7f07000e;
        public static final int abc_btn_radio_material = 0x7f07000f;
        public static final int abc_btn_radio_material_anim = 0x7f070010;
        public static final int abc_btn_radio_to_on_mtrl_000 = 0x7f070011;
        public static final int abc_btn_radio_to_on_mtrl_015 = 0x7f070012;
        public static final int abc_btn_switch_to_on_mtrl_00001 = 0x7f070013;
        public static final int abc_btn_switch_to_on_mtrl_00012 = 0x7f070014;
        public static final int abc_cab_background_internal_bg = 0x7f070015;
        public static final int abc_cab_background_top_material = 0x7f070016;
        public static final int abc_cab_background_top_mtrl_alpha = 0x7f070017;
        public static final int abc_control_background_material = 0x7f070018;
        public static final int abc_dialog_material_background = 0x7f070019;
        public static final int abc_edit_text_material = 0x7f07001a;
        public static final int abc_ic_ab_back_material = 0x7f07001b;
        public static final int abc_ic_arrow_drop_right_black_24dp = 0x7f07001c;
        public static final int abc_ic_clear_material = 0x7f07001d;
        public static final int abc_ic_commit_search_api_mtrl_alpha = 0x7f07001e;
        public static final int abc_ic_go_search_api_material = 0x7f07001f;
        public static final int abc_ic_menu_copy_mtrl_am_alpha = 0x7f070020;
        public static final int abc_ic_menu_cut_mtrl_alpha = 0x7f070021;
        public static final int abc_ic_menu_overflow_material = 0x7f070022;
        public static final int abc_ic_menu_paste_mtrl_am_alpha = 0x7f070023;
        public static final int abc_ic_menu_selectall_mtrl_alpha = 0x7f070024;
        public static final int abc_ic_menu_share_mtrl_alpha = 0x7f070025;
        public static final int abc_ic_search_api_material = 0x7f070026;
        public static final int abc_ic_star_black_16dp = 0x7f070027;
        public static final int abc_ic_star_black_36dp = 0x7f070028;
        public static final int abc_ic_star_black_48dp = 0x7f070029;
        public static final int abc_ic_star_half_black_16dp = 0x7f07002a;
        public static final int abc_ic_star_half_black_36dp = 0x7f07002b;
        public static final int abc_ic_star_half_black_48dp = 0x7f07002c;
        public static final int abc_ic_voice_search_api_material = 0x7f07002d;
        public static final int abc_item_background_holo_dark = 0x7f07002e;
        public static final int abc_item_background_holo_light = 0x7f07002f;
        public static final int abc_list_divider_material = 0x7f070030;
        public static final int abc_list_divider_mtrl_alpha = 0x7f070031;
        public static final int abc_list_focused_holo = 0x7f070032;
        public static final int abc_list_longpressed_holo = 0x7f070033;
        public static final int abc_list_pressed_holo_dark = 0x7f070034;
        public static final int abc_list_pressed_holo_light = 0x7f070035;
        public static final int abc_list_selector_background_transition_holo_dark = 0x7f070036;
        public static final int abc_list_selector_background_transition_holo_light = 0x7f070037;
        public static final int abc_list_selector_disabled_holo_dark = 0x7f070038;
        public static final int abc_list_selector_disabled_holo_light = 0x7f070039;
        public static final int abc_list_selector_holo_dark = 0x7f07003a;
        public static final int abc_list_selector_holo_light = 0x7f07003b;
        public static final int abc_menu_hardkey_panel_mtrl_mult = 0x7f07003c;
        public static final int abc_popup_background_mtrl_mult = 0x7f07003d;
        public static final int abc_ratingbar_indicator_material = 0x7f07003e;
        public static final int abc_ratingbar_material = 0x7f07003f;
        public static final int abc_ratingbar_small_material = 0x7f070040;
        public static final int abc_scrubber_control_off_mtrl_alpha = 0x7f070041;
        public static final int abc_scrubber_control_to_pressed_mtrl_000 = 0x7f070042;
        public static final int abc_scrubber_control_to_pressed_mtrl_005 = 0x7f070043;
        public static final int abc_scrubber_primary_mtrl_alpha = 0x7f070044;
        public static final int abc_scrubber_track_mtrl_alpha = 0x7f070045;
        public static final int abc_seekbar_thumb_material = 0x7f070046;
        public static final int abc_seekbar_tick_mark_material = 0x7f070047;
        public static final int abc_seekbar_track_material = 0x7f070048;
        public static final int abc_spinner_mtrl_am_alpha = 0x7f070049;
        public static final int abc_spinner_textfield_background_material = 0x7f07004a;
        public static final int abc_switch_thumb_material = 0x7f07004b;
        public static final int abc_switch_track_mtrl_alpha = 0x7f07004c;
        public static final int abc_tab_indicator_material = 0x7f07004d;
        public static final int abc_tab_indicator_mtrl_alpha = 0x7f07004e;
        public static final int abc_text_cursor_material = 0x7f07004f;
        public static final int abc_text_select_handle_left_mtrl_dark = 0x7f070050;
        public static final int abc_text_select_handle_left_mtrl_light = 0x7f070051;
        public static final int abc_text_select_handle_middle_mtrl_dark = 0x7f070052;
        public static final int abc_text_select_handle_middle_mtrl_light = 0x7f070053;
        public static final int abc_text_select_handle_right_mtrl_dark = 0x7f070054;
        public static final int abc_text_select_handle_right_mtrl_light = 0x7f070055;
        public static final int abc_textfield_activated_mtrl_alpha = 0x7f070056;
        public static final int abc_textfield_default_mtrl_alpha = 0x7f070057;
        public static final int abc_textfield_search_activated_mtrl_alpha = 0x7f070058;
        public static final int abc_textfield_search_default_mtrl_alpha = 0x7f070059;
        public static final int abc_textfield_search_material = 0x7f07005a;
        public static final int abc_vector_test = 0x7f07005b;
        public static final int btn_checkbox_checked_mtrl = 0x7f070061;
        public static final int btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f070062;
        public static final int btn_checkbox_unchecked_mtrl = 0x7f070063;
        public static final int btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f070064;
        public static final int btn_radio_off_mtrl = 0x7f070065;
        public static final int btn_radio_off_to_on_mtrl_animation = 0x7f070066;
        public static final int btn_radio_on_mtrl = 0x7f070067;
        public static final int btn_radio_on_to_off_mtrl_animation = 0x7f070068;
        public static final int notification_action_background = 0x7f07007c;
        public static final int notification_bg = 0x7f07007d;
        public static final int notification_bg_low = 0x7f07007e;
        public static final int notification_bg_low_normal = 0x7f07007f;
        public static final int notification_bg_low_pressed = 0x7f070080;
        public static final int notification_bg_normal = 0x7f070081;
        public static final int notification_bg_normal_pressed = 0x7f070082;
        public static final int notification_icon_background = 0x7f070083;
        public static final int notification_template_icon_bg = 0x7f070084;
        public static final int notification_template_icon_low_bg = 0x7f070085;
        public static final int notification_tile_bg = 0x7f070086;
        public static final int notify_panel_notification_icon_bg = 0x7f070087;
        public static final int tooltip_frame_dark = 0x7f07008a;
        public static final int tooltip_frame_light = 0x7f07008b;
    }
    public static final class id {
        private id() {}

        public static final int accessibility_action_clickable_span = 0x7f080006;
        public static final int action_bar = 0x7f080007;
        public static final int action_bar_activity_content = 0x7f080008;
        public static final int action_bar_container = 0x7f080009;
        public static final int action_bar_root = 0x7f08000a;
        public static final int action_bar_spinner = 0x7f08000b;
        public static final int action_bar_subtitle = 0x7f08000c;
        public static final int action_bar_title = 0x7f08000d;
        public static final int action_container = 0x7f08000e;
        public static final int action_context_bar = 0x7f08000f;
        public static final int action_divider = 0x7f080010;
        public static final int action_image = 0x7f080011;
        public static final int action_menu_divider = 0x7f080012;
        public static final int action_menu_presenter = 0x7f080013;
        public static final int action_mode_bar = 0x7f080014;
        public static final int action_mode_bar_stub = 0x7f080015;
        public static final int action_mode_close_button = 0x7f080016;
        public static final int action_text = 0x7f080017;
        public static final int actions = 0x7f080019;
        public static final int activity_chooser_view_content = 0x7f08001a;
        public static final int add = 0x7f08001b;
        public static final int alertTitle = 0x7f08001d;
        public static final int async = 0x7f080020;
        public static final int blocking = 0x7f080023;
        public static final int bottom = 0x7f080024;
        public static final int buttonPanel = 0x7f080025;
        public static final int checkbox = 0x7f080029;
        public static final int checked = 0x7f08002a;
        public static final int chronometer = 0x7f08002c;
        public static final int content = 0x7f080032;
        public static final int contentPanel = 0x7f080033;
        public static final int custom = 0x7f080036;
        public static final int customPanel = 0x7f080037;
        public static final int decor_content_parent = 0x7f080039;
        public static final int default_activity_button = 0x7f08003a;
        public static final int dialog_button = 0x7f080043;
        public static final int edit_query = 0x7f080045;
        public static final int end = 0x7f080046;
        public static final int expand_activities_button = 0x7f08004a;
        public static final int expanded_menu = 0x7f08004b;
        public static final int forever = 0x7f080054;
        public static final int group_divider = 0x7f080056;
        public static final int home = 0x7f080057;
        public static final int icon = 0x7f080059;
        public static final int icon_group = 0x7f08005a;
        public static final int image = 0x7f08005c;
        public static final int info = 0x7f08005d;
        public static final int italic = 0x7f08005e;
        public static final int left = 0x7f080062;
        public static final int line1 = 0x7f080063;
        public static final int line3 = 0x7f080064;
        public static final int listMode = 0x7f080065;
        public static final int list_item = 0x7f080066;
        public static final int message = 0x7f080068;
        public static final int multiply = 0x7f08006d;
        public static final int none = 0x7f080071;
        public static final int normal = 0x7f080072;
        public static final int notification_background = 0x7f080073;
        public static final int notification_main_column = 0x7f080074;
        public static final int notification_main_column_container = 0x7f080075;
        public static final int off = 0x7f080076;
        public static final int on = 0x7f080077;
        public static final int parentPanel = 0x7f08007a;
        public static final int progress_circular = 0x7f080080;
        public static final int progress_horizontal = 0x7f080081;
        public static final int radio = 0x7f080083;
        public static final int right = 0x7f080085;
        public static final int right_icon = 0x7f080086;
        public static final int right_side = 0x7f080087;
        public static final int screen = 0x7f080091;
        public static final int scrollIndicatorDown = 0x7f080093;
        public static final int scrollIndicatorUp = 0x7f080094;
        public static final int scrollView = 0x7f080095;
        public static final int search_badge = 0x7f080097;
        public static final int search_bar = 0x7f080098;
        public static final int search_button = 0x7f080099;
        public static final int search_close_btn = 0x7f08009a;
        public static final int search_edit_frame = 0x7f08009b;
        public static final int search_go_btn = 0x7f08009c;
        public static final int search_mag_icon = 0x7f08009d;
        public static final int search_plate = 0x7f08009e;
        public static final int search_src_text = 0x7f08009f;
        public static final int search_voice_btn = 0x7f0800a0;
        public static final int select_dialog_listview = 0x7f0800a1;
        public static final int shortcut = 0x7f0800a3;
        public static final int spacer = 0x7f0800ad;
        public static final int split_action_bar = 0x7f0800ae;
        public static final int src_atop = 0x7f0800af;
        public static final int src_in = 0x7f0800b0;
        public static final int src_over = 0x7f0800b1;
        public static final int start = 0x7f0800b2;
        public static final int submenuarrow = 0x7f0800b7;
        public static final int submit_area = 0x7f0800b8;
        public static final int tabMode = 0x7f0800ba;
        public static final int tag_accessibility_clickable_spans = 0x7f0800bb;
        public static final int tag_accessibility_heading = 0x7f0800bc;
        public static final int tag_accessibility_pane_title = 0x7f0800bd;
        public static final int tag_screen_reader_focusable = 0x7f0800be;
        public static final int tag_transition_group = 0x7f0800bf;
        public static final int tag_unhandled_key_event_manager = 0x7f0800c0;
        public static final int tag_unhandled_key_listeners = 0x7f0800c1;
        public static final int text = 0x7f0800c2;
        public static final int text2 = 0x7f0800c3;
        public static final int textSpacerNoButtons = 0x7f0800c4;
        public static final int textSpacerNoTitle = 0x7f0800c5;
        public static final int time = 0x7f0800cb;
        public static final int title = 0x7f0800cc;
        public static final int titleDividerNoCustom = 0x7f0800cd;
        public static final int title_template = 0x7f0800ce;
        public static final int top = 0x7f0800d0;
        public static final int topPanel = 0x7f0800d1;
        public static final int unchecked = 0x7f0800d8;
        public static final int uniform = 0x7f0800d9;
        public static final int up = 0x7f0800db;
        public static final int wrap_content = 0x7f0800e0;
    }
    public static final class integer {
        private integer() {}

        public static final int abc_config_activityDefaultDur = 0x7f090000;
        public static final int abc_config_activityShortDur = 0x7f090001;
        public static final int cancel_button_image_alpha = 0x7f090004;
        public static final int config_tooltipAnimTime = 0x7f090005;
        public static final int status_bar_notification_info_maxnum = 0x7f09000e;
    }
    public static final class interpolator {
        private interpolator() {}

        public static final int btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000;
        public static final int btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001;
        public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002;
        public static final int btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003;
        public static final int btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004;
        public static final int btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005;
        public static final int fast_out_slow_in = 0x7f0a0006;
    }
    public static final class layout {
        private layout() {}

        public static final int abc_action_bar_title_item = 0x7f0b0000;
        public static final int abc_action_bar_up_container = 0x7f0b0001;
        public static final int abc_action_menu_item_layout = 0x7f0b0002;
        public static final int abc_action_menu_layout = 0x7f0b0003;
        public static final int abc_action_mode_bar = 0x7f0b0004;
        public static final int abc_action_mode_close_item_material = 0x7f0b0005;
        public static final int abc_activity_chooser_view = 0x7f0b0006;
        public static final int abc_activity_chooser_view_list_item = 0x7f0b0007;
        public static final int abc_alert_dialog_button_bar_material = 0x7f0b0008;
        public static final int abc_alert_dialog_material = 0x7f0b0009;
        public static final int abc_alert_dialog_title_material = 0x7f0b000a;
        public static final int abc_cascading_menu_item_layout = 0x7f0b000b;
        public static final int abc_dialog_title_material = 0x7f0b000c;
        public static final int abc_expanded_menu_layout = 0x7f0b000d;
        public static final int abc_list_menu_item_checkbox = 0x7f0b000e;
        public static final int abc_list_menu_item_icon = 0x7f0b000f;
        public static final int abc_list_menu_item_layout = 0x7f0b0010;
        public static final int abc_list_menu_item_radio = 0x7f0b0011;
        public static final int abc_popup_menu_header_item_layout = 0x7f0b0012;
        public static final int abc_popup_menu_item_layout = 0x7f0b0013;
        public static final int abc_screen_content_include = 0x7f0b0014;
        public static final int abc_screen_simple = 0x7f0b0015;
        public static final int abc_screen_simple_overlay_action_mode = 0x7f0b0016;
        public static final int abc_screen_toolbar = 0x7f0b0017;
        public static final int abc_search_dropdown_item_icons_2line = 0x7f0b0018;
        public static final int abc_search_view = 0x7f0b0019;
        public static final int abc_select_dialog_material = 0x7f0b001a;
        public static final int abc_tooltip = 0x7f0b001b;
        public static final int custom_dialog = 0x7f0b0020;
        public static final int notification_action = 0x7f0b0037;
        public static final int notification_action_tombstone = 0x7f0b0038;
        public static final int notification_template_custom_big = 0x7f0b0039;
        public static final int notification_template_icon_group = 0x7f0b003a;
        public static final int notification_template_part_chronometer = 0x7f0b003b;
        public static final int notification_template_part_time = 0x7f0b003c;
        public static final int select_dialog_item_material = 0x7f0b003d;
        public static final int select_dialog_multichoice_material = 0x7f0b003e;
        public static final int select_dialog_singlechoice_material = 0x7f0b003f;
        public static final int support_simple_spinner_dropdown_item = 0x7f0b0040;
    }
    public static final class string {
        private string() {}

        public static final int abc_action_bar_home_description = 0x7f0f0000;
        public static final int abc_action_bar_up_description = 0x7f0f0001;
        public static final int abc_action_menu_overflow_description = 0x7f0f0002;
        public static final int abc_action_mode_done = 0x7f0f0003;
        public static final int abc_activity_chooser_view_see_all = 0x7f0f0004;
        public static final int abc_activitychooserview_choose_application = 0x7f0f0005;
        public static final int abc_capital_off = 0x7f0f0006;
        public static final int abc_capital_on = 0x7f0f0007;
        public static final int abc_menu_alt_shortcut_label = 0x7f0f0008;
        public static final int abc_menu_ctrl_shortcut_label = 0x7f0f0009;
        public static final int abc_menu_delete_shortcut_label = 0x7f0f000a;
        public static final int abc_menu_enter_shortcut_label = 0x7f0f000b;
        public static final int abc_menu_function_shortcut_label = 0x7f0f000c;
        public static final int abc_menu_meta_shortcut_label = 0x7f0f000d;
        public static final int abc_menu_shift_shortcut_label = 0x7f0f000e;
        public static final int abc_menu_space_shortcut_label = 0x7f0f000f;
        public static final int abc_menu_sym_shortcut_label = 0x7f0f0010;
        public static final int abc_prepend_shortcut_label = 0x7f0f0011;
        public static final int abc_search_hint = 0x7f0f0012;
        public static final int abc_searchview_description_clear = 0x7f0f0013;
        public static final int abc_searchview_description_query = 0x7f0f0014;
        public static final int abc_searchview_description_search = 0x7f0f0015;
        public static final int abc_searchview_description_submit = 0x7f0f0016;
        public static final int abc_searchview_description_voice = 0x7f0f0017;
        public static final int abc_shareactionprovider_share_with = 0x7f0f0018;
        public static final int abc_shareactionprovider_share_with_application = 0x7f0f0019;
        public static final int abc_toolbar_collapse_description = 0x7f0f001a;
        public static final int search_menu_title = 0x7f0f004a;
        public static final int status_bar_notification_info_overflow = 0x7f0f004d;
    }
    public static final class style {
        private style() {}

        public static final int AlertDialog_AppCompat = 0x7f100000;
        public static final int AlertDialog_AppCompat_Light = 0x7f100001;
        public static final int Animation_AppCompat_Dialog = 0x7f100009;
        public static final int Animation_AppCompat_DropDownUp = 0x7f10000a;
        public static final int Animation_AppCompat_Tooltip = 0x7f10000b;
        public static final int Base_AlertDialog_AppCompat = 0x7f100012;
        public static final int Base_AlertDialog_AppCompat_Light = 0x7f100013;
        public static final int Base_Animation_AppCompat_Dialog = 0x7f100017;
        public static final int Base_Animation_AppCompat_DropDownUp = 0x7f100018;
        public static final int Base_Animation_AppCompat_Tooltip = 0x7f100019;
        public static final int Base_DialogWindowTitleBackground_AppCompat = 0x7f10001c;
        public static final int Base_DialogWindowTitle_AppCompat = 0x7f10001b;
        public static final int Base_TextAppearance_AppCompat = 0x7f10001d;
        public static final int Base_TextAppearance_AppCompat_Body1 = 0x7f10001e;
        public static final int Base_TextAppearance_AppCompat_Body2 = 0x7f10001f;
        public static final int Base_TextAppearance_AppCompat_Button = 0x7f100020;
        public static final int Base_TextAppearance_AppCompat_Caption = 0x7f100021;
        public static final int Base_TextAppearance_AppCompat_Display1 = 0x7f100022;
        public static final int Base_TextAppearance_AppCompat_Display2 = 0x7f100023;
        public static final int Base_TextAppearance_AppCompat_Display3 = 0x7f100024;
        public static final int Base_TextAppearance_AppCompat_Display4 = 0x7f100025;
        public static final int Base_TextAppearance_AppCompat_Headline = 0x7f100026;
        public static final int Base_TextAppearance_AppCompat_Inverse = 0x7f100027;
        public static final int Base_TextAppearance_AppCompat_Large = 0x7f100028;
        public static final int Base_TextAppearance_AppCompat_Large_Inverse = 0x7f100029;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f10002a;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f10002b;
        public static final int Base_TextAppearance_AppCompat_Medium = 0x7f10002c;
        public static final int Base_TextAppearance_AppCompat_Medium_Inverse = 0x7f10002d;
        public static final int Base_TextAppearance_AppCompat_Menu = 0x7f10002e;
        public static final int Base_TextAppearance_AppCompat_SearchResult = 0x7f10002f;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f100030;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Title = 0x7f100031;
        public static final int Base_TextAppearance_AppCompat_Small = 0x7f100032;
        public static final int Base_TextAppearance_AppCompat_Small_Inverse = 0x7f100033;
        public static final int Base_TextAppearance_AppCompat_Subhead = 0x7f100034;
        public static final int Base_TextAppearance_AppCompat_Subhead_Inverse = 0x7f100035;
        public static final int Base_TextAppearance_AppCompat_Title = 0x7f100036;
        public static final int Base_TextAppearance_AppCompat_Title_Inverse = 0x7f100037;
        public static final int Base_TextAppearance_AppCompat_Tooltip = 0x7f100038;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f100039;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f10003a;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f10003b;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f10003c;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f10003d;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f10003e;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f10003f;
        public static final int Base_TextAppearance_AppCompat_Widget_Button = 0x7f100040;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f100041;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Colored = 0x7f100042;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f100043;
        public static final int Base_TextAppearance_AppCompat_Widget_DropDownItem = 0x7f100044;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f100045;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f100046;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f100047;
        public static final int Base_TextAppearance_AppCompat_Widget_Switch = 0x7f100048;
        public static final int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f100049;
        public static final int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f10004d;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f10004e;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f10004f;
        public static final int Base_ThemeOverlay_AppCompat = 0x7f10006f;
        public static final int Base_ThemeOverlay_AppCompat_ActionBar = 0x7f100070;
        public static final int Base_ThemeOverlay_AppCompat_Dark = 0x7f100071;
        public static final int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f100072;
        public static final int Base_ThemeOverlay_AppCompat_Dialog = 0x7f100073;
        public static final int Base_ThemeOverlay_AppCompat_Dialog_Alert = 0x7f100074;
        public static final int Base_ThemeOverlay_AppCompat_Light = 0x7f100075;
        public static final int Base_Theme_AppCompat = 0x7f100050;
        public static final int Base_Theme_AppCompat_CompactMenu = 0x7f100051;
        public static final int Base_Theme_AppCompat_Dialog = 0x7f100052;
        public static final int Base_Theme_AppCompat_DialogWhenLarge = 0x7f100056;
        public static final int Base_Theme_AppCompat_Dialog_Alert = 0x7f100053;
        public static final int Base_Theme_AppCompat_Dialog_FixedSize = 0x7f100054;
        public static final int Base_Theme_AppCompat_Dialog_MinWidth = 0x7f100055;
        public static final int Base_Theme_AppCompat_Light = 0x7f100057;
        public static final int Base_Theme_AppCompat_Light_DarkActionBar = 0x7f100058;
        public static final int Base_Theme_AppCompat_Light_Dialog = 0x7f100059;
        public static final int Base_Theme_AppCompat_Light_DialogWhenLarge = 0x7f10005d;
        public static final int Base_Theme_AppCompat_Light_Dialog_Alert = 0x7f10005a;
        public static final int Base_Theme_AppCompat_Light_Dialog_FixedSize = 0x7f10005b;
        public static final int Base_Theme_AppCompat_Light_Dialog_MinWidth = 0x7f10005c;
        public static final int Base_V21_ThemeOverlay_AppCompat_Dialog = 0x7f100085;
        public static final int Base_V21_Theme_AppCompat = 0x7f100081;
        public static final int Base_V21_Theme_AppCompat_Dialog = 0x7f100082;
        public static final int Base_V21_Theme_AppCompat_Light = 0x7f100083;
        public static final int Base_V21_Theme_AppCompat_Light_Dialog = 0x7f100084;
        public static final int Base_V22_Theme_AppCompat = 0x7f100086;
        public static final int Base_V22_Theme_AppCompat_Light = 0x7f100087;
        public static final int Base_V23_Theme_AppCompat = 0x7f100088;
        public static final int Base_V23_Theme_AppCompat_Light = 0x7f100089;
        public static final int Base_V26_Theme_AppCompat = 0x7f10008a;
        public static final int Base_V26_Theme_AppCompat_Light = 0x7f10008b;
        public static final int Base_V26_Widget_AppCompat_Toolbar = 0x7f10008c;
        public static final int Base_V28_Theme_AppCompat = 0x7f10008d;
        public static final int Base_V28_Theme_AppCompat_Light = 0x7f10008e;
        public static final int Base_V7_ThemeOverlay_AppCompat_Dialog = 0x7f100093;
        public static final int Base_V7_Theme_AppCompat = 0x7f10008f;
        public static final int Base_V7_Theme_AppCompat_Dialog = 0x7f100090;
        public static final int Base_V7_Theme_AppCompat_Light = 0x7f100091;
        public static final int Base_V7_Theme_AppCompat_Light_Dialog = 0x7f100092;
        public static final int Base_V7_Widget_AppCompat_AutoCompleteTextView = 0x7f100094;
        public static final int Base_V7_Widget_AppCompat_EditText = 0x7f100095;
        public static final int Base_V7_Widget_AppCompat_Toolbar = 0x7f100096;
        public static final int Base_Widget_AppCompat_ActionBar = 0x7f100097;
        public static final int Base_Widget_AppCompat_ActionBar_Solid = 0x7f100098;
        public static final int Base_Widget_AppCompat_ActionBar_TabBar = 0x7f100099;
        public static final int Base_Widget_AppCompat_ActionBar_TabText = 0x7f10009a;
        public static final int Base_Widget_AppCompat_ActionBar_TabView = 0x7f10009b;
        public static final int Base_Widget_AppCompat_ActionButton = 0x7f10009c;
        public static final int Base_Widget_AppCompat_ActionButton_CloseMode = 0x7f10009d;
        public static final int Base_Widget_AppCompat_ActionButton_Overflow = 0x7f10009e;
        public static final int Base_Widget_AppCompat_ActionMode = 0x7f10009f;
        public static final int Base_Widget_AppCompat_ActivityChooserView = 0x7f1000a0;
        public static final int Base_Widget_AppCompat_AutoCompleteTextView = 0x7f1000a1;
        public static final int Base_Widget_AppCompat_Button = 0x7f1000a2;
        public static final int Base_Widget_AppCompat_ButtonBar = 0x7f1000a8;
        public static final int Base_Widget_AppCompat_ButtonBar_AlertDialog = 0x7f1000a9;
        public static final int Base_Widget_AppCompat_Button_Borderless = 0x7f1000a3;
        public static final int Base_Widget_AppCompat_Button_Borderless_Colored = 0x7f1000a4;
        public static final int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f1000a5;
        public static final int Base_Widget_AppCompat_Button_Colored = 0x7f1000a6;
        public static final int Base_Widget_AppCompat_Button_Small = 0x7f1000a7;
        public static final int Base_Widget_AppCompat_CompoundButton_CheckBox = 0x7f1000aa;
        public static final int Base_Widget_AppCompat_CompoundButton_RadioButton = 0x7f1000ab;
        public static final int Base_Widget_AppCompat_CompoundButton_Switch = 0x7f1000ac;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle = 0x7f1000ad;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle_Common = 0x7f1000ae;
        public static final int Base_Widget_AppCompat_DropDownItem_Spinner = 0x7f1000af;
        public static final int Base_Widget_AppCompat_EditText = 0x7f1000b0;
        public static final int Base_Widget_AppCompat_ImageButton = 0x7f1000b1;
        public static final int Base_Widget_AppCompat_Light_ActionBar = 0x7f1000b2;
        public static final int Base_Widget_AppCompat_Light_ActionBar_Solid = 0x7f1000b3;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabBar = 0x7f1000b4;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText = 0x7f1000b5;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f1000b6;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabView = 0x7f1000b7;
        public static final int Base_Widget_AppCompat_Light_PopupMenu = 0x7f1000b8;
        public static final int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f1000b9;
        public static final int Base_Widget_AppCompat_ListMenuView = 0x7f1000ba;
        public static final int Base_Widget_AppCompat_ListPopupWindow = 0x7f1000bb;
        public static final int Base_Widget_AppCompat_ListView = 0x7f1000bc;
        public static final int Base_Widget_AppCompat_ListView_DropDown = 0x7f1000bd;
        public static final int Base_Widget_AppCompat_ListView_Menu = 0x7f1000be;
        public static final int Base_Widget_AppCompat_PopupMenu = 0x7f1000bf;
        public static final int Base_Widget_AppCompat_PopupMenu_Overflow = 0x7f1000c0;
        public static final int Base_Widget_AppCompat_PopupWindow = 0x7f1000c1;
        public static final int Base_Widget_AppCompat_ProgressBar = 0x7f1000c2;
        public static final int Base_Widget_AppCompat_ProgressBar_Horizontal = 0x7f1000c3;
        public static final int Base_Widget_AppCompat_RatingBar = 0x7f1000c4;
        public static final int Base_Widget_AppCompat_RatingBar_Indicator = 0x7f1000c5;
        public static final int Base_Widget_AppCompat_RatingBar_Small = 0x7f1000c6;
        public static final int Base_Widget_AppCompat_SearchView = 0x7f1000c7;
        public static final int Base_Widget_AppCompat_SearchView_ActionBar = 0x7f1000c8;
        public static final int Base_Widget_AppCompat_SeekBar = 0x7f1000c9;
        public static final int Base_Widget_AppCompat_SeekBar_Discrete = 0x7f1000ca;
        public static final int Base_Widget_AppCompat_Spinner = 0x7f1000cb;
        public static final int Base_Widget_AppCompat_Spinner_Underlined = 0x7f1000cc;
        public static final int Base_Widget_AppCompat_TextView = 0x7f1000cd;
        public static final int Base_Widget_AppCompat_TextView_SpinnerItem = 0x7f1000ce;
        public static final int Base_Widget_AppCompat_Toolbar = 0x7f1000cf;
        public static final int Base_Widget_AppCompat_Toolbar_Button_Navigation = 0x7f1000d0;
        public static final int Platform_AppCompat = 0x7f1000da;
        public static final int Platform_AppCompat_Light = 0x7f1000db;
        public static final int Platform_ThemeOverlay_AppCompat = 0x7f1000e0;
        public static final int Platform_ThemeOverlay_AppCompat_Dark = 0x7f1000e1;
        public static final int Platform_ThemeOverlay_AppCompat_Light = 0x7f1000e2;
        public static final int Platform_V21_AppCompat = 0x7f1000e3;
        public static final int Platform_V21_AppCompat_Light = 0x7f1000e4;
        public static final int Platform_V25_AppCompat = 0x7f1000e5;
        public static final int Platform_V25_AppCompat_Light = 0x7f1000e6;
        public static final int Platform_Widget_AppCompat_Spinner = 0x7f1000e7;
        public static final int RtlOverlay_DialogWindowTitle_AppCompat = 0x7f1000e8;
        public static final int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 0x7f1000e9;
        public static final int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 0x7f1000ea;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem = 0x7f1000eb;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 0x7f1000ec;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut = 0x7f1000ed;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow = 0x7f1000ee;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 0x7f1000ef;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Title = 0x7f1000f0;
        public static final int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 0x7f1000f6;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown = 0x7f1000f1;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 0x7f1000f2;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 0x7f1000f3;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 0x7f1000f4;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 0x7f1000f5;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton = 0x7f1000f7;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 0x7f1000f8;
        public static final int TextAppearance_AppCompat = 0x7f10010a;
        public static final int TextAppearance_AppCompat_Body1 = 0x7f10010b;
        public static final int TextAppearance_AppCompat_Body2 = 0x7f10010c;
        public static final int TextAppearance_AppCompat_Button = 0x7f10010d;
        public static final int TextAppearance_AppCompat_Caption = 0x7f10010e;
        public static final int TextAppearance_AppCompat_Display1 = 0x7f10010f;
        public static final int TextAppearance_AppCompat_Display2 = 0x7f100110;
        public static final int TextAppearance_AppCompat_Display3 = 0x7f100111;
        public static final int TextAppearance_AppCompat_Display4 = 0x7f100112;
        public static final int TextAppearance_AppCompat_Headline = 0x7f100113;
        public static final int TextAppearance_AppCompat_Inverse = 0x7f100114;
        public static final int TextAppearance_AppCompat_Large = 0x7f100115;
        public static final int TextAppearance_AppCompat_Large_Inverse = 0x7f100116;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 0x7f100117;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Title = 0x7f100118;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f100119;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f10011a;
        public static final int TextAppearance_AppCompat_Medium = 0x7f10011b;
        public static final int TextAppearance_AppCompat_Medium_Inverse = 0x7f10011c;
        public static final int TextAppearance_AppCompat_Menu = 0x7f10011d;
        public static final int TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f10011e;
        public static final int TextAppearance_AppCompat_SearchResult_Title = 0x7f10011f;
        public static final int TextAppearance_AppCompat_Small = 0x7f100120;
        public static final int TextAppearance_AppCompat_Small_Inverse = 0x7f100121;
        public static final int TextAppearance_AppCompat_Subhead = 0x7f100122;
        public static final int TextAppearance_AppCompat_Subhead_Inverse = 0x7f100123;
        public static final int TextAppearance_AppCompat_Title = 0x7f100124;
        public static final int TextAppearance_AppCompat_Title_Inverse = 0x7f100125;
        public static final int TextAppearance_AppCompat_Tooltip = 0x7f100126;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f100127;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f100128;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f100129;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f10012a;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f10012b;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f10012c;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 0x7f10012d;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f10012e;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 0x7f10012f;
        public static final int TextAppearance_AppCompat_Widget_Button = 0x7f100130;
        public static final int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f100131;
        public static final int TextAppearance_AppCompat_Widget_Button_Colored = 0x7f100132;
        public static final int TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f100133;
        public static final int TextAppearance_AppCompat_Widget_DropDownItem = 0x7f100134;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f100135;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f100136;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f100137;
        public static final int TextAppearance_AppCompat_Widget_Switch = 0x7f100138;
        public static final int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f100139;
        public static final int TextAppearance_Compat_Notification = 0x7f10013a;
        public static final int TextAppearance_Compat_Notification_Info = 0x7f10013b;
        public static final int TextAppearance_Compat_Notification_Line2 = 0x7f10013c;
        public static final int TextAppearance_Compat_Notification_Time = 0x7f10013d;
        public static final int TextAppearance_Compat_Notification_Title = 0x7f10013e;
        public static final int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f100155;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f100156;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f100157;
        public static final int ThemeOverlay_AppCompat = 0x7f100190;
        public static final int ThemeOverlay_AppCompat_ActionBar = 0x7f100191;
        public static final int ThemeOverlay_AppCompat_Dark = 0x7f100192;
        public static final int ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f100193;
        public static final int ThemeOverlay_AppCompat_Dialog = 0x7f100194;
        public static final int ThemeOverlay_AppCompat_Dialog_Alert = 0x7f100195;
        public static final int ThemeOverlay_AppCompat_Light = 0x7f100196;
        public static final int Theme_AppCompat = 0x7f100158;
        public static final int Theme_AppCompat_CompactMenu = 0x7f100159;
        public static final int Theme_AppCompat_DayNight = 0x7f10015a;
        public static final int Theme_AppCompat_DayNight_DarkActionBar = 0x7f10015b;
        public static final int Theme_AppCompat_DayNight_Dialog = 0x7f10015c;
        public static final int Theme_AppCompat_DayNight_DialogWhenLarge = 0x7f10015f;
        public static final int Theme_AppCompat_DayNight_Dialog_Alert = 0x7f10015d;
        public static final int Theme_AppCompat_DayNight_Dialog_MinWidth = 0x7f10015e;
        public static final int Theme_AppCompat_DayNight_NoActionBar = 0x7f100160;
        public static final int Theme_AppCompat_Dialog = 0x7f100161;
        public static final int Theme_AppCompat_DialogWhenLarge = 0x7f100164;
        public static final int Theme_AppCompat_Dialog_Alert = 0x7f100162;
        public static final int Theme_AppCompat_Dialog_MinWidth = 0x7f100163;
        public static final int Theme_AppCompat_Light = 0x7f100165;
        public static final int Theme_AppCompat_Light_DarkActionBar = 0x7f100166;
        public static final int Theme_AppCompat_Light_Dialog = 0x7f100167;
        public static final int Theme_AppCompat_Light_DialogWhenLarge = 0x7f10016a;
        public static final int Theme_AppCompat_Light_Dialog_Alert = 0x7f100168;
        public static final int Theme_AppCompat_Light_Dialog_MinWidth = 0x7f100169;
        public static final int Theme_AppCompat_Light_NoActionBar = 0x7f10016b;
        public static final int Theme_AppCompat_NoActionBar = 0x7f10016c;
        public static final int Widget_AppCompat_ActionBar = 0x7f1001ab;
        public static final int Widget_AppCompat_ActionBar_Solid = 0x7f1001ac;
        public static final int Widget_AppCompat_ActionBar_TabBar = 0x7f1001ad;
        public static final int Widget_AppCompat_ActionBar_TabText = 0x7f1001ae;
        public static final int Widget_AppCompat_ActionBar_TabView = 0x7f1001af;
        public static final int Widget_AppCompat_ActionButton = 0x7f1001b0;
        public static final int Widget_AppCompat_ActionButton_CloseMode = 0x7f1001b1;
        public static final int Widget_AppCompat_ActionButton_Overflow = 0x7f1001b2;
        public static final int Widget_AppCompat_ActionMode = 0x7f1001b3;
        public static final int Widget_AppCompat_ActivityChooserView = 0x7f1001b4;
        public static final int Widget_AppCompat_AutoCompleteTextView = 0x7f1001b5;
        public static final int Widget_AppCompat_Button = 0x7f1001b6;
        public static final int Widget_AppCompat_ButtonBar = 0x7f1001bc;
        public static final int Widget_AppCompat_ButtonBar_AlertDialog = 0x7f1001bd;
        public static final int Widget_AppCompat_Button_Borderless = 0x7f1001b7;
        public static final int Widget_AppCompat_Button_Borderless_Colored = 0x7f1001b8;
        public static final int Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f1001b9;
        public static final int Widget_AppCompat_Button_Colored = 0x7f1001ba;
        public static final int Widget_AppCompat_Button_Small = 0x7f1001bb;
        public static final int Widget_AppCompat_CompoundButton_CheckBox = 0x7f1001be;
        public static final int Widget_AppCompat_CompoundButton_RadioButton = 0x7f1001bf;
        public static final int Widget_AppCompat_CompoundButton_Switch = 0x7f1001c0;
        public static final int Widget_AppCompat_DrawerArrowToggle = 0x7f1001c1;
        public static final int Widget_AppCompat_DropDownItem_Spinner = 0x7f1001c2;
        public static final int Widget_AppCompat_EditText = 0x7f1001c3;
        public static final int Widget_AppCompat_ImageButton = 0x7f1001c4;
        public static final int Widget_AppCompat_Light_ActionBar = 0x7f1001c5;
        public static final int Widget_AppCompat_Light_ActionBar_Solid = 0x7f1001c6;
        public static final int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 0x7f1001c7;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar = 0x7f1001c8;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 0x7f1001c9;
        public static final int Widget_AppCompat_Light_ActionBar_TabText = 0x7f1001ca;
        public static final int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f1001cb;
        public static final int Widget_AppCompat_Light_ActionBar_TabView = 0x7f1001cc;
        public static final int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 0x7f1001cd;
        public static final int Widget_AppCompat_Light_ActionButton = 0x7f1001ce;
        public static final int Widget_AppCompat_Light_ActionButton_CloseMode = 0x7f1001cf;
        public static final int Widget_AppCompat_Light_ActionButton_Overflow = 0x7f1001d0;
        public static final int Widget_AppCompat_Light_ActionMode_Inverse = 0x7f1001d1;
        public static final int Widget_AppCompat_Light_ActivityChooserView = 0x7f1001d2;
        public static final int Widget_AppCompat_Light_AutoCompleteTextView = 0x7f1001d3;
        public static final int Widget_AppCompat_Light_DropDownItem_Spinner = 0x7f1001d4;
        public static final int Widget_AppCompat_Light_ListPopupWindow = 0x7f1001d5;
        public static final int Widget_AppCompat_Light_ListView_DropDown = 0x7f1001d6;
        public static final int Widget_AppCompat_Light_PopupMenu = 0x7f1001d7;
        public static final int Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f1001d8;
        public static final int Widget_AppCompat_Light_SearchView = 0x7f1001d9;
        public static final int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 0x7f1001da;
        public static final int Widget_AppCompat_ListMenuView = 0x7f1001db;
        public static final int Widget_AppCompat_ListPopupWindow = 0x7f1001dc;
        public static final int Widget_AppCompat_ListView = 0x7f1001dd;
        public static final int Widget_AppCompat_ListView_DropDown = 0x7f1001de;
        public static final int Widget_AppCompat_ListView_Menu = 0x7f1001df;
        public static final int Widget_AppCompat_PopupMenu = 0x7f1001e0;
        public static final int Widget_AppCompat_PopupMenu_Overflow = 0x7f1001e1;
        public static final int Widget_AppCompat_PopupWindow = 0x7f1001e2;
        public static final int Widget_AppCompat_ProgressBar = 0x7f1001e3;
        public static final int Widget_AppCompat_ProgressBar_Horizontal = 0x7f1001e4;
        public static final int Widget_AppCompat_RatingBar = 0x7f1001e5;
        public static final int Widget_AppCompat_RatingBar_Indicator = 0x7f1001e6;
        public static final int Widget_AppCompat_RatingBar_Small = 0x7f1001e7;
        public static final int Widget_AppCompat_SearchView = 0x7f1001e8;
        public static final int Widget_AppCompat_SearchView_ActionBar = 0x7f1001e9;
        public static final int Widget_AppCompat_SeekBar = 0x7f1001ea;
        public static final int Widget_AppCompat_SeekBar_Discrete = 0x7f1001eb;
        public static final int Widget_AppCompat_Spinner = 0x7f1001ec;
        public static final int Widget_AppCompat_Spinner_DropDown = 0x7f1001ed;
        public static final int Widget_AppCompat_Spinner_DropDown_ActionBar = 0x7f1001ee;
        public static final int Widget_AppCompat_Spinner_Underlined = 0x7f1001ef;
        public static final int Widget_AppCompat_TextView = 0x7f1001f0;
        public static final int Widget_AppCompat_TextView_SpinnerItem = 0x7f1001f1;
        public static final int Widget_AppCompat_Toolbar = 0x7f1001f2;
        public static final int Widget_AppCompat_Toolbar_Button_Navigation = 0x7f1001f3;
        public static final int Widget_Compat_NotificationActionContainer = 0x7f1001f4;
        public static final int Widget_Compat_NotificationActionText = 0x7f1001f5;
        public static final int Widget_Support_CoordinatorLayout = 0x7f100230;
    }
    public static final class styleable {
        private styleable() {}

        public static final int[] ActionBar = { 0x7f030036, 0x7f03003c, 0x7f03003d, 0x7f0300ac, 0x7f0300ad, 0x7f0300ae, 0x7f0300af, 0x7f0300b0, 0x7f0300b1, 0x7f0300cb, 0x7f0300d0, 0x7f0300d1, 0x7f0300e2, 0x7f03010f, 0x7f030115, 0x7f03011b, 0x7f03011c, 0x7f03011e, 0x7f030128, 0x7f030133, 0x7f03015b, 0x7f030169, 0x7f03017a, 0x7f03017e, 0x7f03017f, 0x7f0301b2, 0x7f0301b5, 0x7f0301fb, 0x7f030205 };
        public static final int ActionBar_background = 0;
        public static final int ActionBar_backgroundSplit = 1;
        public static final int ActionBar_backgroundStacked = 2;
        public static final int ActionBar_contentInsetEnd = 3;
        public static final int ActionBar_contentInsetEndWithActions = 4;
        public static final int ActionBar_contentInsetLeft = 5;
        public static final int ActionBar_contentInsetRight = 6;
        public static final int ActionBar_contentInsetStart = 7;
        public static final int ActionBar_contentInsetStartWithNavigation = 8;
        public static final int ActionBar_customNavigationLayout = 9;
        public static final int ActionBar_displayOptions = 10;
        public static final int ActionBar_divider = 11;
        public static final int ActionBar_elevation = 12;
        public static final int ActionBar_height = 13;
        public static final int ActionBar_hideOnContentScroll = 14;
        public static final int ActionBar_homeAsUpIndicator = 15;
        public static final int ActionBar_homeLayout = 16;
        public static final int ActionBar_icon = 17;
        public static final int ActionBar_indeterminateProgressStyle = 18;
        public static final int ActionBar_itemPadding = 19;
        public static final int ActionBar_logo = 20;
        public static final int ActionBar_navigationMode = 21;
        public static final int ActionBar_popupTheme = 22;
        public static final int ActionBar_progressBarPadding = 23;
        public static final int ActionBar_progressBarStyle = 24;
        public static final int ActionBar_subtitle = 25;
        public static final int ActionBar_subtitleTextStyle = 26;
        public static final int ActionBar_title = 27;
        public static final int ActionBar_titleTextStyle = 28;
        public static final int[] ActionBarLayout = { 0x10100b3 };
        public static final int ActionBarLayout_android_layout_gravity = 0;
        public static final int[] ActionMenuItemView = { 0x101013f };
        public static final int ActionMenuItemView_android_minWidth = 0;
        public static final int[] ActionMenuView = {  };
        public static final int[] ActionMode = { 0x7f030036, 0x7f03003c, 0x7f03008b, 0x7f03010f, 0x7f0301b5, 0x7f030205 };
        public static final int ActionMode_background = 0;
        public static final int ActionMode_backgroundSplit = 1;
        public static final int ActionMode_closeItemLayout = 2;
        public static final int ActionMode_height = 3;
        public static final int ActionMode_subtitleTextStyle = 4;
        public static final int ActionMode_titleTextStyle = 5;
        public static final int[] ActivityChooserView = { 0x7f0300e9, 0x7f030129 };
        public static final int ActivityChooserView_expandActivityOverflowButtonDrawable = 0;
        public static final int ActivityChooserView_initialActivityCount = 1;
        public static final int[] AlertDialog = { 0x10100f2, 0x7f03005d, 0x7f03005e, 0x7f030150, 0x7f030151, 0x7f030166, 0x7f03019a, 0x7f03019b };
        public static final int AlertDialog_android_layout = 0;
        public static final int AlertDialog_buttonIconDimen = 1;
        public static final int AlertDialog_buttonPanelSideLayout = 2;
        public static final int AlertDialog_listItemLayout = 3;
        public static final int AlertDialog_listLayout = 4;
        public static final int AlertDialog_multiChoiceItemLayout = 5;
        public static final int AlertDialog_showTitle = 6;
        public static final int AlertDialog_singleChoiceItemLayout = 7;
        public static final int[] AnimatedStateListDrawableCompat = { 0x101011c, 0x1010194, 0x1010195, 0x1010196, 0x101030c, 0x101030d };
        public static final int AnimatedStateListDrawableCompat_android_dither = 0;
        public static final int AnimatedStateListDrawableCompat_android_visible = 1;
        public static final int AnimatedStateListDrawableCompat_android_variablePadding = 2;
        public static final int AnimatedStateListDrawableCompat_android_constantSize = 3;
        public static final int AnimatedStateListDrawableCompat_android_enterFadeDuration = 4;
        public static final int AnimatedStateListDrawableCompat_android_exitFadeDuration = 5;
        public static final int[] AnimatedStateListDrawableItem = { 0x10100d0, 0x1010199 };
        public static final int AnimatedStateListDrawableItem_android_id = 0;
        public static final int AnimatedStateListDrawableItem_android_drawable = 1;
        public static final int[] AnimatedStateListDrawableTransition = { 0x1010199, 0x1010449, 0x101044a, 0x101044b };
        public static final int AnimatedStateListDrawableTransition_android_drawable = 0;
        public static final int AnimatedStateListDrawableTransition_android_toId = 1;
        public static final int AnimatedStateListDrawableTransition_android_fromId = 2;
        public static final int AnimatedStateListDrawableTransition_android_reversible = 3;
        public static final int[] AppCompatImageView = { 0x1010119, 0x7f0301a5, 0x7f0301f9, 0x7f0301fa };
        public static final int AppCompatImageView_android_src = 0;
        public static final int AppCompatImageView_srcCompat = 1;
        public static final int AppCompatImageView_tint = 2;
        public static final int AppCompatImageView_tintMode = 3;
        public static final int[] AppCompatSeekBar = { 0x1010142, 0x7f0301f6, 0x7f0301f7, 0x7f0301f8 };
        public static final int AppCompatSeekBar_android_thumb = 0;
        public static final int AppCompatSeekBar_tickMark = 1;
        public static final int AppCompatSeekBar_tickMarkTint = 2;
        public static final int AppCompatSeekBar_tickMarkTintMode = 3;
        public static final int[] AppCompatTextHelper = { 0x1010034, 0x101016d, 0x101016e, 0x101016f, 0x1010170, 0x1010392, 0x1010393 };
        public static final int AppCompatTextHelper_android_textAppearance = 0;
        public static final int AppCompatTextHelper_android_drawableTop = 1;
        public static final int AppCompatTextHelper_android_drawableBottom = 2;
        public static final int AppCompatTextHelper_android_drawableLeft = 3;
        public static final int AppCompatTextHelper_android_drawableRight = 4;
        public static final int AppCompatTextHelper_android_drawableStart = 5;
        public static final int AppCompatTextHelper_android_drawableEnd = 6;
        public static final int[] AppCompatTextView = { 0x1010034, 0x7f030031, 0x7f030032, 0x7f030033, 0x7f030034, 0x7f030035, 0x7f0300d5, 0x7f0300d6, 0x7f0300d7, 0x7f0300d8, 0x7f0300da, 0x7f0300db, 0x7f0300fe, 0x7f030101, 0x7f030109, 0x7f03013b, 0x7f03014a, 0x7f0301d5, 0x7f0301ef };
        public static final int AppCompatTextView_android_textAppearance = 0;
        public static final int AppCompatTextView_autoSizeMaxTextSize = 1;
        public static final int AppCompatTextView_autoSizeMinTextSize = 2;
        public static final int AppCompatTextView_autoSizePresetSizes = 3;
        public static final int AppCompatTextView_autoSizeStepGranularity = 4;
        public static final int AppCompatTextView_autoSizeTextType = 5;
        public static final int AppCompatTextView_drawableBottomCompat = 6;
        public static final int AppCompatTextView_drawableEndCompat = 7;
        public static final int AppCompatTextView_drawableLeftCompat = 8;
        public static final int AppCompatTextView_drawableRightCompat = 9;
        public static final int AppCompatTextView_drawableStartCompat = 10;
        public static final int AppCompatTextView_drawableTopCompat = 11;
        public static final int AppCompatTextView_firstBaselineToTopHeight = 12;
        public static final int AppCompatTextView_fontFamily = 13;
        public static final int AppCompatTextView_fontVariationSettings = 14;
        public static final int AppCompatTextView_lastBaselineToBottomHeight = 15;
        public static final int AppCompatTextView_lineHeight = 16;
        public static final int AppCompatTextView_textAllCaps = 17;
        public static final int AppCompatTextView_textLocale = 18;
        public static final int[] AppCompatTheme = { 0x1010057, 0x10100ae, 0x7f030000, 0x7f030001, 0x7f030002, 0x7f030003, 0x7f030004, 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000e, 0x7f03000f, 0x7f030010, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030014, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f030022, 0x7f030023, 0x7f030024, 0x7f030025, 0x7f030026, 0x7f030030, 0x7f030048, 0x7f030056, 0x7f030057, 0x7f030058, 0x7f030059, 0x7f03005a, 0x7f03005f, 0x7f030060, 0x7f03006a, 0x7f03006f, 0x7f030091, 0x7f030092, 0x7f030093, 0x7f030094, 0x7f030095, 0x7f030096, 0x7f030097, 0x7f0300a3, 0x7f0300a4, 0x7f0300a9, 0x7f0300b8, 0x7f0300cd, 0x7f0300ce, 0x7f0300cf, 0x7f0300d2, 0x7f0300d4, 0x7f0300dd, 0x7f0300de, 0x7f0300df, 0x7f0300e0, 0x7f0300e1, 0x7f03011b, 0x7f030127, 0x7f03014c, 0x7f03014d, 0x7f03014e, 0x7f03014f, 0x7f030152, 0x7f030153, 0x7f030154, 0x7f030155, 0x7f030156, 0x7f030157, 0x7f030158, 0x7f030159, 0x7f03015a, 0x7f030171, 0x7f030172, 0x7f030173, 0x7f030179, 0x7f03017b, 0x7f030182, 0x7f030183, 0x7f030184, 0x7f030185, 0x7f03018d, 0x7f03018e, 0x7f03018f, 0x7f030190, 0x7f0301a2, 0x7f0301a3, 0x7f0301b9, 0x7f0301e0, 0x7f0301e1, 0x7f0301e2, 0x7f0301e3, 0x7f0301e5, 0x7f0301e6, 0x7f0301e7, 0x7f0301e8, 0x7f0301eb, 0x7f0301ec, 0x7f030207, 0x7f030208, 0x7f030209, 0x7f03020a, 0x7f030212, 0x7f030214, 0x7f030215, 0x7f030216, 0x7f030217, 0x7f030218, 0x7f030219, 0x7f03021a, 0x7f03021b, 0x7f03021c, 0x7f03021d };
        public static final int AppCompatTheme_android_windowIsFloating = 0;
        public static final int AppCompatTheme_android_windowAnimationStyle = 1;
        public static final int AppCompatTheme_actionBarDivider = 2;
        public static final int AppCompatTheme_actionBarItemBackground = 3;
        public static final int AppCompatTheme_actionBarPopupTheme = 4;
        public static final int AppCompatTheme_actionBarSize = 5;
        public static final int AppCompatTheme_actionBarSplitStyle = 6;
        public static final int AppCompatTheme_actionBarStyle = 7;
        public static final int AppCompatTheme_actionBarTabBarStyle = 8;
        public static final int AppCompatTheme_actionBarTabStyle = 9;
        public static final int AppCompatTheme_actionBarTabTextStyle = 10;
        public static final int AppCompatTheme_actionBarTheme = 11;
        public static final int AppCompatTheme_actionBarWidgetTheme = 12;
        public static final int AppCompatTheme_actionButtonStyle = 13;
        public static final int AppCompatTheme_actionDropDownStyle = 14;
        public static final int AppCompatTheme_actionMenuTextAppearance = 15;
        public static final int AppCompatTheme_actionMenuTextColor = 16;
        public static final int AppCompatTheme_actionModeBackground = 17;
        public static final int AppCompatTheme_actionModeCloseButtonStyle = 18;
        public static final int AppCompatTheme_actionModeCloseDrawable = 19;
        public static final int AppCompatTheme_actionModeCopyDrawable = 20;
        public static final int AppCompatTheme_actionModeCutDrawable = 21;
        public static final int AppCompatTheme_actionModeFindDrawable = 22;
        public static final int AppCompatTheme_actionModePasteDrawable = 23;
        public static final int AppCompatTheme_actionModePopupWindowStyle = 24;
        public static final int AppCompatTheme_actionModeSelectAllDrawable = 25;
        public static final int AppCompatTheme_actionModeShareDrawable = 26;
        public static final int AppCompatTheme_actionModeSplitBackground = 27;
        public static final int AppCompatTheme_actionModeStyle = 28;
        public static final int AppCompatTheme_actionModeWebSearchDrawable = 29;
        public static final int AppCompatTheme_actionOverflowButtonStyle = 30;
        public static final int AppCompatTheme_actionOverflowMenuStyle = 31;
        public static final int AppCompatTheme_activityChooserViewStyle = 32;
        public static final int AppCompatTheme_alertDialogButtonGroupStyle = 33;
        public static final int AppCompatTheme_alertDialogCenterButtons = 34;
        public static final int AppCompatTheme_alertDialogStyle = 35;
        public static final int AppCompatTheme_alertDialogTheme = 36;
        public static final int AppCompatTheme_autoCompleteTextViewStyle = 37;
        public static final int AppCompatTheme_borderlessButtonStyle = 38;
        public static final int AppCompatTheme_buttonBarButtonStyle = 39;
        public static final int AppCompatTheme_buttonBarNegativeButtonStyle = 40;
        public static final int AppCompatTheme_buttonBarNeutralButtonStyle = 41;
        public static final int AppCompatTheme_buttonBarPositiveButtonStyle = 42;
        public static final int AppCompatTheme_buttonBarStyle = 43;
        public static final int AppCompatTheme_buttonStyle = 44;
        public static final int AppCompatTheme_buttonStyleSmall = 45;
        public static final int AppCompatTheme_checkboxStyle = 46;
        public static final int AppCompatTheme_checkedTextViewStyle = 47;
        public static final int AppCompatTheme_colorAccent = 48;
        public static final int AppCompatTheme_colorBackgroundFloating = 49;
        public static final int AppCompatTheme_colorButtonNormal = 50;
        public static final int AppCompatTheme_colorControlActivated = 51;
        public static final int AppCompatTheme_colorControlHighlight = 52;
        public static final int AppCompatTheme_colorControlNormal = 53;
        public static final int AppCompatTheme_colorError = 54;
        public static final int AppCompatTheme_colorPrimary = 55;
        public static final int AppCompatTheme_colorPrimaryDark = 56;
        public static final int AppCompatTheme_colorSwitchThumbNormal = 57;
        public static final int AppCompatTheme_controlBackground = 58;
        public static final int AppCompatTheme_dialogCornerRadius = 59;
        public static final int AppCompatTheme_dialogPreferredPadding = 60;
        public static final int AppCompatTheme_dialogTheme = 61;
        public static final int AppCompatTheme_dividerHorizontal = 62;
        public static final int AppCompatTheme_dividerVertical = 63;
        public static final int AppCompatTheme_dropDownListViewStyle = 64;
        public static final int AppCompatTheme_dropdownListPreferredItemHeight = 65;
        public static final int AppCompatTheme_editTextBackground = 66;
        public static final int AppCompatTheme_editTextColor = 67;
        public static final int AppCompatTheme_editTextStyle = 68;
        public static final int AppCompatTheme_homeAsUpIndicator = 69;
        public static final int AppCompatTheme_imageButtonStyle = 70;
        public static final int AppCompatTheme_listChoiceBackgroundIndicator = 71;
        public static final int AppCompatTheme_listChoiceIndicatorMultipleAnimated = 72;
        public static final int AppCompatTheme_listChoiceIndicatorSingleAnimated = 73;
        public static final int AppCompatTheme_listDividerAlertDialog = 74;
        public static final int AppCompatTheme_listMenuViewStyle = 75;
        public static final int AppCompatTheme_listPopupWindowStyle = 76;
        public static final int AppCompatTheme_listPreferredItemHeight = 77;
        public static final int AppCompatTheme_listPreferredItemHeightLarge = 78;
        public static final int AppCompatTheme_listPreferredItemHeightSmall = 79;
        public static final int AppCompatTheme_listPreferredItemPaddingEnd = 80;
        public static final int AppCompatTheme_listPreferredItemPaddingLeft = 81;
        public static final int AppCompatTheme_listPreferredItemPaddingRight = 82;
        public static final int AppCompatTheme_listPreferredItemPaddingStart = 83;
        public static final int AppCompatTheme_panelBackground = 84;
        public static final int AppCompatTheme_panelMenuListTheme = 85;
        public static final int AppCompatTheme_panelMenuListWidth = 86;
        public static final int AppCompatTheme_popupMenuStyle = 87;
        public static final int AppCompatTheme_popupWindowStyle = 88;
        public static final int AppCompatTheme_radioButtonStyle = 89;
        public static final int AppCompatTheme_ratingBarStyle = 90;
        public static final int AppCompatTheme_ratingBarStyleIndicator = 91;
        public static final int AppCompatTheme_ratingBarStyleSmall = 92;
        public static final int AppCompatTheme_searchViewStyle = 93;
        public static final int AppCompatTheme_seekBarStyle = 94;
        public static final int AppCompatTheme_selectableItemBackground = 95;
        public static final int AppCompatTheme_selectableItemBackgroundBorderless = 96;
        public static final int AppCompatTheme_spinnerDropDownItemStyle = 97;
        public static final int AppCompatTheme_spinnerStyle = 98;
        public static final int AppCompatTheme_switchStyle = 99;
        public static final int AppCompatTheme_textAppearanceLargePopupMenu = 100;
        public static final int AppCompatTheme_textAppearanceListItem = 101;
        public static final int AppCompatTheme_textAppearanceListItemSecondary = 102;
        public static final int AppCompatTheme_textAppearanceListItemSmall = 103;
        public static final int AppCompatTheme_textAppearancePopupMenuHeader = 104;
        public static final int AppCompatTheme_textAppearanceSearchResultSubtitle = 105;
        public static final int AppCompatTheme_textAppearanceSearchResultTitle = 106;
        public static final int AppCompatTheme_textAppearanceSmallPopupMenu = 107;
        public static final int AppCompatTheme_textColorAlertDialogListItem = 108;
        public static final int AppCompatTheme_textColorSearchUrl = 109;
        public static final int AppCompatTheme_toolbarNavigationButtonStyle = 110;
        public static final int AppCompatTheme_toolbarStyle = 111;
        public static final int AppCompatTheme_tooltipForegroundColor = 112;
        public static final int AppCompatTheme_tooltipFrameBackground = 113;
        public static final int AppCompatTheme_viewInflaterClass = 114;
        public static final int AppCompatTheme_windowActionBar = 115;
        public static final int AppCompatTheme_windowActionBarOverlay = 116;
        public static final int AppCompatTheme_windowActionModeOverlay = 117;
        public static final int AppCompatTheme_windowFixedHeightMajor = 118;
        public static final int AppCompatTheme_windowFixedHeightMinor = 119;
        public static final int AppCompatTheme_windowFixedWidthMajor = 120;
        public static final int AppCompatTheme_windowFixedWidthMinor = 121;
        public static final int AppCompatTheme_windowMinWidthMajor = 122;
        public static final int AppCompatTheme_windowMinWidthMinor = 123;
        public static final int AppCompatTheme_windowNoTitle = 124;
        public static final int[] ButtonBarLayout = { 0x7f03002a };
        public static final int ButtonBarLayout_allowStacking = 0;
        public static final int[] ColorStateListItem = { 0x10101a5, 0x101031f, 0x7f03002b };
        public static final int ColorStateListItem_android_color = 0;
        public static final int ColorStateListItem_android_alpha = 1;
        public static final int ColorStateListItem_alpha = 2;
        public static final int[] CompoundButton = { 0x1010107, 0x7f03005b, 0x7f030061, 0x7f030062 };
        public static final int CompoundButton_android_button = 0;
        public static final int CompoundButton_buttonCompat = 1;
        public static final int CompoundButton_buttonTint = 2;
        public static final int CompoundButton_buttonTintMode = 3;
        public static final int[] CoordinatorLayout = { 0x7f030139, 0x7f0301ac };
        public static final int CoordinatorLayout_keylines = 0;
        public static final int CoordinatorLayout_statusBarBackground = 1;
        public static final int[] CoordinatorLayout_Layout = { 0x10100b3, 0x7f03013e, 0x7f03013f, 0x7f030140, 0x7f030143, 0x7f030144, 0x7f030145 };
        public static final int CoordinatorLayout_Layout_android_layout_gravity = 0;
        public static final int CoordinatorLayout_Layout_layout_anchor = 1;
        public static final int CoordinatorLayout_Layout_layout_anchorGravity = 2;
        public static final int CoordinatorLayout_Layout_layout_behavior = 3;
        public static final int CoordinatorLayout_Layout_layout_dodgeInsetEdges = 4;
        public static final int CoordinatorLayout_Layout_layout_insetEdge = 5;
        public static final int CoordinatorLayout_Layout_layout_keyline = 6;
        public static final int[] DrawerArrowToggle = { 0x7f03002e, 0x7f03002f, 0x7f030040, 0x7f030090, 0x7f0300d9, 0x7f03010c, 0x7f0301a1, 0x7f0301f2 };
        public static final int DrawerArrowToggle_arrowHeadLength = 0;
        public static final int DrawerArrowToggle_arrowShaftLength = 1;
        public static final int DrawerArrowToggle_barLength = 2;
        public static final int DrawerArrowToggle_color = 3;
        public static final int DrawerArrowToggle_drawableSize = 4;
        public static final int DrawerArrowToggle_gapBetweenBars = 5;
        public static final int DrawerArrowToggle_spinBars = 6;
        public static final int DrawerArrowToggle_thickness = 7;
        public static final int[] FontFamily = { 0x7f030102, 0x7f030103, 0x7f030104, 0x7f030105, 0x7f030106, 0x7f030107 };
        public static final int FontFamily_fontProviderAuthority = 0;
        public static final int FontFamily_fontProviderCerts = 1;
        public static final int FontFamily_fontProviderFetchStrategy = 2;
        public static final int FontFamily_fontProviderFetchTimeout = 3;
        public static final int FontFamily_fontProviderPackage = 4;
        public static final int FontFamily_fontProviderQuery = 5;
        public static final int[] FontFamilyFont = { 0x1010532, 0x1010533, 0x101053f, 0x101056f, 0x1010570, 0x7f030100, 0x7f030108, 0x7f030109, 0x7f03010a, 0x7f03020f };
        public static final int FontFamilyFont_android_font = 0;
        public static final int FontFamilyFont_android_fontWeight = 1;
        public static final int FontFamilyFont_android_fontStyle = 2;
        public static final int FontFamilyFont_android_ttcIndex = 3;
        public static final int FontFamilyFont_android_fontVariationSettings = 4;
        public static final int FontFamilyFont_font = 5;
        public static final int FontFamilyFont_fontStyle = 6;
        public static final int FontFamilyFont_fontVariationSettings = 7;
        public static final int FontFamilyFont_fontWeight = 8;
        public static final int FontFamilyFont_ttcIndex = 9;
        public static final int[] GradientColor = { 0x101019d, 0x101019e, 0x10101a1, 0x10101a2, 0x10101a3, 0x10101a4, 0x1010201, 0x101020b, 0x1010510, 0x1010511, 0x1010512, 0x1010513 };
        public static final int GradientColor_android_startColor = 0;
        public static final int GradientColor_android_endColor = 1;
        public static final int GradientColor_android_type = 2;
        public static final int GradientColor_android_centerX = 3;
        public static final int GradientColor_android_centerY = 4;
        public static final int GradientColor_android_gradientRadius = 5;
        public static final int GradientColor_android_tileMode = 6;
        public static final int GradientColor_android_centerColor = 7;
        public static final int GradientColor_android_startX = 8;
        public static final int GradientColor_android_startY = 9;
        public static final int GradientColor_android_endX = 10;
        public static final int GradientColor_android_endY = 11;
        public static final int[] GradientColorItem = { 0x10101a5, 0x1010514 };
        public static final int GradientColorItem_android_color = 0;
        public static final int GradientColorItem_android_offset = 1;
        public static final int[] LinearLayoutCompat = { 0x10100af, 0x10100c4, 0x1010126, 0x1010127, 0x1010128, 0x7f0300d1, 0x7f0300d3, 0x7f030163, 0x7f030197 };
        public static final int LinearLayoutCompat_android_gravity = 0;
        public static final int LinearLayoutCompat_android_orientation = 1;
        public static final int LinearLayoutCompat_android_baselineAligned = 2;
        public static final int LinearLayoutCompat_android_baselineAlignedChildIndex = 3;
        public static final int LinearLayoutCompat_android_weightSum = 4;
        public static final int LinearLayoutCompat_divider = 5;
        public static final int LinearLayoutCompat_dividerPadding = 6;
        public static final int LinearLayoutCompat_measureWithLargestChild = 7;
        public static final int LinearLayoutCompat_showDividers = 8;
        public static final int[] LinearLayoutCompat_Layout = { 0x10100b3, 0x10100f4, 0x10100f5, 0x1010181 };
        public static final int LinearLayoutCompat_Layout_android_layout_gravity = 0;
        public static final int LinearLayoutCompat_Layout_android_layout_width = 1;
        public static final int LinearLayoutCompat_Layout_android_layout_height = 2;
        public static final int LinearLayoutCompat_Layout_android_layout_weight = 3;
        public static final int[] ListPopupWindow = { 0x10102ac, 0x10102ad };
        public static final int ListPopupWindow_android_dropDownHorizontalOffset = 0;
        public static final int ListPopupWindow_android_dropDownVerticalOffset = 1;
        public static final int[] MenuGroup = { 0x101000e, 0x10100d0, 0x1010194, 0x10101de, 0x10101df, 0x10101e0 };
        public static final int MenuGroup_android_enabled = 0;
        public static final int MenuGroup_android_id = 1;
        public static final int MenuGroup_android_visible = 2;
        public static final int MenuGroup_android_menuCategory = 3;
        public static final int MenuGroup_android_orderInCategory = 4;
        public static final int MenuGroup_android_checkableBehavior = 5;
        public static final int[] MenuItem = { 0x1010002, 0x101000e, 0x10100d0, 0x1010106, 0x1010194, 0x10101de, 0x10101df, 0x10101e1, 0x10101e2, 0x10101e3, 0x10101e4, 0x10101e5, 0x101026f, 0x7f03000d, 0x7f03001f, 0x7f030021, 0x7f03002c, 0x7f0300ab, 0x7f030124, 0x7f030125, 0x7f03016b, 0x7f030196, 0x7f03020b };
        public static final int MenuItem_android_icon = 0;
        public static final int MenuItem_android_enabled = 1;
        public static final int MenuItem_android_id = 2;
        public static final int MenuItem_android_checked = 3;
        public static final int MenuItem_android_visible = 4;
        public static final int MenuItem_android_menuCategory = 5;
        public static final int MenuItem_android_orderInCategory = 6;
        public static final int MenuItem_android_title = 7;
        public static final int MenuItem_android_titleCondensed = 8;
        public static final int MenuItem_android_alphabeticShortcut = 9;
        public static final int MenuItem_android_numericShortcut = 10;
        public static final int MenuItem_android_checkable = 11;
        public static final int MenuItem_android_onClick = 12;
        public static final int MenuItem_actionLayout = 13;
        public static final int MenuItem_actionProviderClass = 14;
        public static final int MenuItem_actionViewClass = 15;
        public static final int MenuItem_alphabeticModifiers = 16;
        public static final int MenuItem_contentDescription = 17;
        public static final int MenuItem_iconTint = 18;
        public static final int MenuItem_iconTintMode = 19;
        public static final int MenuItem_numericModifiers = 20;
        public static final int MenuItem_showAsAction = 21;
        public static final int MenuItem_tooltipText = 22;
        public static final int[] MenuView = { 0x10100ae, 0x101012c, 0x101012d, 0x101012e, 0x101012f, 0x1010130, 0x1010131, 0x7f03017c, 0x7f0301b0 };
        public static final int MenuView_android_windowAnimationStyle = 0;
        public static final int MenuView_android_itemTextAppearance = 1;
        public static final int MenuView_android_horizontalDivider = 2;
        public static final int MenuView_android_verticalDivider = 3;
        public static final int MenuView_android_headerBackground = 4;
        public static final int MenuView_android_itemBackground = 5;
        public static final int MenuView_android_itemIconDisabledAlpha = 6;
        public static final int MenuView_preserveIconSpacing = 7;
        public static final int MenuView_subMenuArrow = 8;
        public static final int[] PopupWindow = { 0x1010176, 0x10102c9, 0x7f03016c };
        public static final int PopupWindow_android_popupBackground = 0;
        public static final int PopupWindow_android_popupAnimationStyle = 1;
        public static final int PopupWindow_overlapAnchor = 2;
        public static final int[] PopupWindowBackgroundState = { 0x7f0301a7 };
        public static final int PopupWindowBackgroundState_state_above_anchor = 0;
        public static final int[] RecycleListView = { 0x7f03016d, 0x7f030170 };
        public static final int RecycleListView_paddingBottomNoButtons = 0;
        public static final int RecycleListView_paddingTopNoTitle = 1;
        public static final int[] SearchView = { 0x10100da, 0x101011f, 0x1010220, 0x1010264, 0x7f030084, 0x7f0300aa, 0x7f0300cc, 0x7f03010d, 0x7f030126, 0x7f03013c, 0x7f030180, 0x7f030181, 0x7f03018b, 0x7f03018c, 0x7f0301b1, 0x7f0301b6, 0x7f030213 };
        public static final int SearchView_android_focusable = 0;
        public static final int SearchView_android_maxWidth = 1;
        public static final int SearchView_android_inputType = 2;
        public static final int SearchView_android_imeOptions = 3;
        public static final int SearchView_closeIcon = 4;
        public static final int SearchView_commitIcon = 5;
        public static final int SearchView_defaultQueryHint = 6;
        public static final int SearchView_goIcon = 7;
        public static final int SearchView_iconifiedByDefault = 8;
        public static final int SearchView_layout = 9;
        public static final int SearchView_queryBackground = 10;
        public static final int SearchView_queryHint = 11;
        public static final int SearchView_searchHintIcon = 12;
        public static final int SearchView_searchIcon = 13;
        public static final int SearchView_submitBackground = 14;
        public static final int SearchView_suggestionRowLayout = 15;
        public static final int SearchView_voiceIcon = 16;
        public static final int[] Spinner = { 0x10100b2, 0x1010176, 0x101017b, 0x1010262, 0x7f03017a };
        public static final int Spinner_android_entries = 0;
        public static final int Spinner_android_popupBackground = 1;
        public static final int Spinner_android_prompt = 2;
        public static final int Spinner_android_dropDownWidth = 3;
        public static final int Spinner_popupTheme = 4;
        public static final int[] StateListDrawable = { 0x101011c, 0x1010194, 0x1010195, 0x1010196, 0x101030c, 0x101030d };
        public static final int StateListDrawable_android_dither = 0;
        public static final int StateListDrawable_android_visible = 1;
        public static final int StateListDrawable_android_variablePadding = 2;
        public static final int StateListDrawable_android_constantSize = 3;
        public static final int StateListDrawable_android_enterFadeDuration = 4;
        public static final int StateListDrawable_android_exitFadeDuration = 5;
        public static final int[] StateListDrawableItem = { 0x1010199 };
        public static final int StateListDrawableItem_android_drawable = 0;
        public static final int[] SwitchCompat = { 0x1010124, 0x1010125, 0x1010142, 0x7f030199, 0x7f0301a4, 0x7f0301b7, 0x7f0301b8, 0x7f0301ba, 0x7f0301f3, 0x7f0301f4, 0x7f0301f5, 0x7f03020c, 0x7f03020d, 0x7f03020e };
        public static final int SwitchCompat_android_textOn = 0;
        public static final int SwitchCompat_android_textOff = 1;
        public static final int SwitchCompat_android_thumb = 2;
        public static final int SwitchCompat_showText = 3;
        public static final int SwitchCompat_splitTrack = 4;
        public static final int SwitchCompat_switchMinWidth = 5;
        public static final int SwitchCompat_switchPadding = 6;
        public static final int SwitchCompat_switchTextAppearance = 7;
        public static final int SwitchCompat_thumbTextPadding = 8;
        public static final int SwitchCompat_thumbTint = 9;
        public static final int SwitchCompat_thumbTintMode = 10;
        public static final int SwitchCompat_track = 11;
        public static final int SwitchCompat_trackTint = 12;
        public static final int SwitchCompat_trackTintMode = 13;
        public static final int[] TextAppearance = { 0x1010095, 0x1010096, 0x1010097, 0x1010098, 0x101009a, 0x101009b, 0x1010161, 0x1010162, 0x1010163, 0x1010164, 0x10103ac, 0x7f030101, 0x7f030109, 0x7f0301d5, 0x7f0301ef };
        public static final int TextAppearance_android_textSize = 0;
        public static final int TextAppearance_android_typeface = 1;
        public static final int TextAppearance_android_textStyle = 2;
        public static final int TextAppearance_android_textColor = 3;
        public static final int TextAppearance_android_textColorHint = 4;
        public static final int TextAppearance_android_textColorLink = 5;
        public static final int TextAppearance_android_shadowColor = 6;
        public static final int TextAppearance_android_shadowDx = 7;
        public static final int TextAppearance_android_shadowDy = 8;
        public static final int TextAppearance_android_shadowRadius = 9;
        public static final int TextAppearance_android_fontFamily = 10;
        public static final int TextAppearance_fontFamily = 11;
        public static final int TextAppearance_fontVariationSettings = 12;
        public static final int TextAppearance_textAllCaps = 13;
        public static final int TextAppearance_textLocale = 14;
        public static final int[] Toolbar = { 0x10100af, 0x1010140, 0x7f03005c, 0x7f03008c, 0x7f03008d, 0x7f0300ac, 0x7f0300ad, 0x7f0300ae, 0x7f0300af, 0x7f0300b0, 0x7f0300b1, 0x7f03015b, 0x7f03015c, 0x7f030161, 0x7f030167, 0x7f030168, 0x7f03017a, 0x7f0301b2, 0x7f0301b3, 0x7f0301b4, 0x7f0301fb, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030202, 0x7f030203, 0x7f030204 };
        public static final int Toolbar_android_gravity = 0;
        public static final int Toolbar_android_minHeight = 1;
        public static final int Toolbar_buttonGravity = 2;
        public static final int Toolbar_collapseContentDescription = 3;
        public static final int Toolbar_collapseIcon = 4;
        public static final int Toolbar_contentInsetEnd = 5;
        public static final int Toolbar_contentInsetEndWithActions = 6;
        public static final int Toolbar_contentInsetLeft = 7;
        public static final int Toolbar_contentInsetRight = 8;
        public static final int Toolbar_contentInsetStart = 9;
        public static final int Toolbar_contentInsetStartWithNavigation = 10;
        public static final int Toolbar_logo = 11;
        public static final int Toolbar_logoDescription = 12;
        public static final int Toolbar_maxButtonHeight = 13;
        public static final int Toolbar_navigationContentDescription = 14;
        public static final int Toolbar_navigationIcon = 15;
        public static final int Toolbar_popupTheme = 16;
        public static final int Toolbar_subtitle = 17;
        public static final int Toolbar_subtitleTextAppearance = 18;
        public static final int Toolbar_subtitleTextColor = 19;
        public static final int Toolbar_title = 20;
        public static final int Toolbar_titleMargin = 21;
        public static final int Toolbar_titleMarginBottom = 22;
        public static final int Toolbar_titleMarginEnd = 23;
        public static final int Toolbar_titleMarginStart = 24;
        public static final int Toolbar_titleMarginTop = 25;
        public static final int Toolbar_titleMargins = 26;
        public static final int Toolbar_titleTextAppearance = 27;
        public static final int Toolbar_titleTextColor = 28;
        public static final int[] View = { 0x1010000, 0x10100da, 0x7f03016e, 0x7f03016f, 0x7f0301f1 };
        public static final int View_android_theme = 0;
        public static final int View_android_focusable = 1;
        public static final int View_paddingEnd = 2;
        public static final int View_paddingStart = 3;
        public static final int View_theme = 4;
        public static final int[] ViewBackgroundHelper = { 0x10100d4, 0x7f03003e, 0x7f03003f };
        public static final int ViewBackgroundHelper_android_background = 0;
        public static final int ViewBackgroundHelper_backgroundTint = 1;
        public static final int ViewBackgroundHelper_backgroundTintMode = 2;
        public static final int[] ViewStubCompat = { 0x10100d0, 0x10100f2, 0x10100f3 };
        public static final int ViewStubCompat_android_id = 0;
        public static final int ViewStubCompat_android_layout = 1;
        public static final int ViewStubCompat_android_inflatedId = 2;
    }
}
