# BES蓝牙连接流程图

## 整体连接流程

```mermaid
flowchart TD
    A[应用启动] --> B[初始化蓝牙适配器]
    B --> C{蓝牙是否启用?}
    C -->|否| D[请求启用蓝牙]
    C -->|是| E[开始设备扫描]
    D --> E
    
    E --> F[发现目标设备]
    F --> G[停止扫描]
    G --> H[建立SPP连接]
    
    H --> I{连接是否成功?}
    I -->|否| J[连接失败处理]
    I -->|是| K[连接成功]
    
    K --> L[启动数据传输线程]
    L --> M[开始数据通信]
    
    M --> N{连接是否断开?}
    N -->|否| M
    N -->|是| O[清理资源]
    
    J --> P[重试连接?]
    P -->|是| H
    P -->|否| O
    
    O --> Q[结束]
```

## 设备扫描详细流程

```mermaid
sequenceDiagram
    participant App as 应用层
    participant Scanner as ClassicScanner
    participant Adapter as BluetoothAdapter
    participant System as Android系统

    App->>Scanner: 创建扫描器实例
    App->>Scanner: startScan(callback)
    
    Scanner->>Scanner: 检查扫描状态
    alt 已在扫描中
        Scanner-->>App: 直接返回
    else 未在扫描
        Scanner->>Adapter: startDiscovery()
        Adapter->>System: 开始蓝牙设备发现
        
        System-->>Scanner: ACTION_DISCOVERY_STARTED
        Scanner->>App: callback.onScanStart()
        
        loop 设备发现过程
            System-->>Scanner: ACTION_FOUND
            Scanner->>Scanner: 解析设备信息
            Scanner->>App: callback.onFound(device, rssi)
        end
        
        System-->>Scanner: ACTION_DISCOVERY_FINISHED
        Scanner->>App: callback.onScanFinish()
    end
```

## SPP连接建立流程

```mermaid
sequenceDiagram
    participant App as 应用层
    participant Connector as SppConnector
    participant Thread as ConnectRunnable
    participant Socket as BluetoothSocket

    App->>Connector: connect(deviceAddress)
    Connector->>Connector: 检查连接状态
    
    alt 已连接或连接中
        Connector-->>App: 返回false
    else 未连接
        Connector->>Thread: 创建连接线程
        Connector->>Thread: 启动线程
        
        Thread->>Socket: createInsecureRfcommSocketToServiceRecord(UUID)
        Thread->>Socket: connect()
        
        alt 连接成功
            Socket-->>Thread: 连接建立
            Thread->>Connector: 创建ConnectedRunnable
            Thread->>Connector: onConnectionStateChanged(true)
            Connector->>App: callback.onConnectionStateChanged(true)
            Thread->>Thread: 启动数据传输线程
        else 连接失败
            Socket-->>Thread: IOException
            Thread->>Connector: onConnectionStateChanged(false)
            Connector->>App: callback.onConnectionStateChanged(false)
        end
    end
```

## 数据传输流程

```mermaid
flowchart TD
    A[ConnectedRunnable启动] --> B[初始化输入输出流]
    B --> C[进入数据读取循环]
    
    C --> D[从InputStream读取数据]
    D --> E{读取到数据?}
    E -->|否| F[继续等待]
    E -->|是| G[数据长度 > 0?]
    
    G -->|否| F
    G -->|是| H[提取有效数据]
    H --> I[调用onReceive回调]
    I --> J[传递给SppMessageHelper]
    
    J --> K[数据包解析]
    K --> L{是否为完整包?}
    L -->|否| M[缓存数据等待更多]
    L -->|是| N[识别数据类型]
    
    N --> O{音频数据?}
    O -->|是| P[处理音频流]
    O -->|否| Q[处理控制指令]
    
    P --> R[回调应用层]
    Q --> R
    R --> F
    
    F --> S{连接是否断开?}
    S -->|否| D
    S -->|是| T[退出循环]
    T --> U[清理资源]
```

## 数据包解析流程

```mermaid
flowchart TD
    A[接收原始数据] --> B[与缓存数据合并]
    B --> C[检查数据长度]
    C --> D{长度 > 1?}
    D -->|否| E[等待更多数据]
    D -->|是| F[检查包头]
    
    F --> G{0xFF 0xFF?}
    G -->|是| H[音频数据流]
    G -->|否| I[检查控制指令]
    
    I --> J{指令类型?}
    J -->|0x07| K[5字节控制指令]
    J -->|0x04| L[5字节开始标志]
    J -->|0x05| M[4字节结束标志]
    J -->|0x01| N[12字节流开始]
    J -->|0x03| O[16字节流结束]
    J -->|0x00| P[可变长度指令]
    
    K --> Q[验证长度]
    L --> Q
    M --> Q
    N --> Q
    O --> Q
    P --> R[读取长度字段]
    R --> Q
    
    Q --> S{数据完整?}
    S -->|否| E
    S -->|是| T[提取数据包]
    T --> U[更新缓存]
    U --> V[返回完整包]
    
    H --> W[直接处理音频]
    W --> V
```

## 状态转换图

```mermaid
stateDiagram-v2
    [*] --> Disconnected: 初始状态
    
    Disconnected --> Connecting: connect()调用
    Connecting --> Connected: Socket连接成功
    Connecting --> Disconnected: 连接失败/超时
    
    Connected --> Disconnected: disconnect()调用
    Connected --> Disconnected: 连接异常
    Connected --> Connected: 数据传输
    
    Disconnected --> [*]: 资源清理
    
    note right of Connecting
        状态码: 1
        创建Socket
        建立连接
    end note
    
    note right of Connected
        状态码: 2
        数据传输线程运行
        处理读写操作
    end note
    
    note right of Disconnected
        状态码: 0
        Socket已关闭
        线程已停止
    end note
```

## 错误处理流程

```mermaid
flowchart TD
    A[操作执行] --> B{是否发生异常?}
    B -->|否| C[正常执行]
    B -->|是| D[异常类型判断]
    
    D --> E{IOException?}
    E -->|是| F[连接相关错误]
    E -->|否| G{SecurityException?}
    G -->|是| H[权限相关错误]
    G -->|否| I[其他异常]
    
    F --> J[记录错误日志]
    H --> J
    I --> J
    
    J --> K[触发状态变更]
    K --> L[通知应用层]
    L --> M[清理资源]
    
    M --> N{需要重试?}
    N -->|是| O[延迟重试]
    N -->|否| P[结束处理]
    
    O --> Q[重试次数检查]
    Q --> R{超过最大次数?}
    R -->|是| P
    R -->|否| S[执行重试]
    S --> A
    
    C --> T[继续执行]
```

## 内存管理流程

```mermaid
flowchart TD
    A[组件创建] --> B[资源分配]
    B --> C[正常使用]
    
    C --> D{需要断开?}
    D -->|否| C
    D -->|是| E[开始清理]
    
    E --> F[关闭BluetoothSocket]
    F --> G[关闭InputStream]
    G --> H[关闭OutputStream]
    H --> I[停止工作线程]
    I --> J[清理回调列表]
    J --> K[清理数据缓存]
    K --> L[重置状态变量]
    L --> M[释放完成]
    
    M --> N{应用退出?}
    N -->|是| O[最终清理]
    N -->|否| P[等待下次使用]
    
    O --> Q[注销广播接收器]
    Q --> R[清理单例引用]
    R --> S[GC回收]
```

## 线程交互图

```mermaid
sequenceDiagram
    participant Main as 主线程
    participant Connect as 连接线程
    participant Data as 数据线程
    participant Callback as 回调处理

    Main->>Connect: 启动连接
    Connect->>Connect: 建立Socket连接
    
    alt 连接成功
        Connect->>Data: 启动数据传输线程
        Connect->>Callback: 连接成功回调
        Callback->>Main: 通知UI更新
        
        loop 数据传输
            Data->>Data: 读取数据
            Data->>Callback: 数据接收回调
            Callback->>Main: 通知数据处理
            
            Main->>Data: 发送数据请求
            Data->>Data: 写入数据
        end
        
        Data->>Callback: 连接断开回调
        Callback->>Main: 通知连接断开
    else 连接失败
        Connect->>Callback: 连接失败回调
        Callback->>Main: 通知连接失败
    end
```
