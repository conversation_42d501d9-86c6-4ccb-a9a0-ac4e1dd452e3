package com.bes.opus;

import android.util.Log;

import java.util.ArrayList;
import java.util.List;

/**
 * Opus音频解码器
 *
 * 提供Opus编码音频数据的解码功能，支持单帧解码和批量解码。
 * 通过JNI调用底层native库实现高效的音频解码处理。
 *
 * 主要功能：
 * - 初始化解码器参数
 * - 单帧Opus数据解码为PCM
 * - 批量解码多帧Opus数据
 * - 获取解码器配置参数
 *
 * <AUTHOR>
 * @since 2017/12/07
 */
public class OpusDecoder {

    /** 日志标签 */
    private String TAG = "OpusDecoder";

    /** 解码器实例指针，指向native层解码器对象 */
    private static long decoder;

    static {
        // 加载native库，包含Opus解码的底层实现
        System.loadLibrary("besOpus");
    }

    /**
     * 初始化native解码器
     * @param framePeriod 帧周期
     * @param singleFrameInputSize 单帧输入大小
     * @param totalFrameInputSize 总帧输入大小
     * @param pcmOutputSize PCM输出大小
     * @param bitRate 比特率
     * @return 解码器实例指针
     */
    private static native long init_native(int framePeriod, int singleFrameInputSize, int totalFrameInputSize, int pcmOutputSize, int bitRate);

    /**
     * 获取单帧解码器输入数据长度
     * @return 单帧输入数据字节长度
     */
    private static native int getOpusSingleFrameDecoderIntLen_native();

    /**
     * 获取总帧解码器输入数据长度
     * @return 总帧输入数据字节长度
     */
    private static native int getOpusFrameTotalFrameDecoderIntLen_native();

    /**
     * 解码单帧Opus数据为PCM短整型数组
     * @param decoder 解码器实例指针
     * @param decodeDataIn 待解码的Opus数据
     * @return 解码后的PCM短整型数组
     */
    private native short[] decode_native(long decoder, byte[] decodeDataIn);

    /**
     * 销毁native解码器，释放资源
     * @param decoder 解码器实例指针
     */
    private native void destory_native(long decoder);

    /**
     * 初始化解码器
     * @param framePeriod 帧周期
     * @param singleFrameInputSize 单帧输入大小
     * @param totalFrameInputSize 总帧输入大小
     * @param pcmOutputSize PCM输出大小
     * @param bitRate 比特率
     */
    public static void init(int framePeriod, int singleFrameInputSize, int totalFrameInputSize, int pcmOutputSize, int bitRate) {
        decoder = init_native(framePeriod, singleFrameInputSize, totalFrameInputSize, pcmOutputSize, bitRate);
    }

    /**
     * 获取单帧解码器输入数据的字节长度
     * 根据HuyaDecoder.h配置的opus值返回设置解码输入的字节数/帧
     * @return 单帧输入数据字节长度
     */
    public static int getOpusSingleFrameDecoderIntLen() {
        return getOpusSingleFrameDecoderIntLen_native();
    }

    /**
     * 获取总帧解码器输入数据的字节长度
     * @return 总帧输入数据字节长度
     */
    public static int getOpusTotalFrameDecoderIntLen() {
        return getOpusFrameTotalFrameDecoderIntLen_native();
    }

    /**
     * 解码单帧Opus数据为PCM字节数组
     * @param decodeDataIn 待解码的Opus数据，长度必须等于getOpusSingleFrameDecoderIntLen()返回值
     * @return 解码后的PCM字节数组，解码失败返回null
     */
    public byte[] decode(byte[] decodeDataIn) {
        // 如果解码器未初始化，使用默认参数初始化
        if (decoder == 0) {
            init(0, 0, 0, 0, 0); // 使用默认设置
        }
        // 检查输入数据有效性
        if (decodeDataIn == null || decodeDataIn.length != getOpusSingleFrameDecoderIntLen()) {
            return null;
        }
        // 调用native方法解码为短整型数组
        short[] shorts = decode_native(decoder, decodeDataIn);
        if (shorts != null) {
            // 转换为字节数组返回
            return OpusUtils.Shorts2Bytes(shorts);
        }
        return null;
    }

    /**
     * 批量解码多帧Opus数据
     * @param totalDataIn 包含多帧Opus数据的字节数组，长度必须是单帧长度的整数倍
     * @return 解码后的完整PCM字节数组，解码失败返回null
     */
    public byte[] decodeAll(byte[] totalDataIn) {
        // 检查输入数据有效性，长度必须是单帧长度的整数倍
        if (totalDataIn == null || totalDataIn.length % getOpusSingleFrameDecoderIntLen() != 0) {
            return null;
        }

        byte[] totalDatas = null;
        int frameLen = getOpusSingleFrameDecoderIntLen();
        int frameCount = totalDataIn.length / frameLen;

        // 逐帧解码并拼接结果
        for (int i = 0; i < frameCount; i++) {
            byte[] frame = new byte[frameLen];
            System.arraycopy(totalDataIn, i * frameLen, frame, 0, frameLen);

            if (frame != null) {
                byte[] decodedFrame = decode(frame);
                if (decodedFrame != null) {
                    if (totalDatas == null) {
                        // 第一帧直接赋值
                        totalDatas = decodedFrame;
                    } else {
                        // 后续帧拼接到总数据中
                        int newLen = totalDatas.length + decodedFrame.length;
                        Log.e(TAG, "newLen LEN = " + newLen);
                        byte[] newDatas = new byte[newLen];
                        System.arraycopy(totalDatas, 0, newDatas, 0, totalDatas.length);
                        System.arraycopy(decodedFrame, 0, newDatas, totalDatas.length, decodedFrame.length);
                        totalDatas = newDatas;
                    }
                }
            }
        }

        if (totalDatas != null) {
            Log.e(TAG, "TOTAL DECODE OUT = " + totalDatas.length);
        }
        return totalDatas;
    }

    /**
     * 销毁解码器，释放native资源
     * 注意：方法名应为destroy，这里保持原有拼写以维持API兼容性
     */
    public void descory() {
        destory_native(decoder);
    }
}
