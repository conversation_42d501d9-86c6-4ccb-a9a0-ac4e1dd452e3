# BES蓝牙框架数据交互详解

## 数据交互架构

### 整体数据流向

```mermaid
flowchart TD
    A[应用层数据] --> B[SppConnector]
    B --> C[ConnectedRunnable]
    C --> D[BluetoothSocket OutputStream]
    D --> E[蓝牙硬件层]
    E --> F[远程设备]
    
    F --> G[蓝牙硬件层]
    G --> H[BluetoothSocket InputStream]
    H --> I[ConnectedRunnable读取循环]
    I --> J[SppMessageHelper解析]
    J --> K[数据包重组]
    K --> L[回调应用层]
    
    subgraph "发送路径"
        A --> B --> C --> D --> E --> F
    end
    
    subgraph "接收路径"
        F --> G --> H --> I --> J --> K --> L
    end
```

### 数据交互层次

```
┌─────────────────────────────────────────────────────────────┐
│                    应用业务层                                │
│  ┌─────────────────┐    ┌─────────────────────────────────┐  │
│  │   音频数据处理   │    │        控制指令处理            │  │
│  └─────────────────┘    └─────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    协议解析层                                │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │              SppMessageHelper                          │  │
│  │  ┌─────────────────┐    ┌─────────────────────────────┐ │  │
│  │  │   数据包识别     │    │        缓存管理            │ │  │
│  │  └─────────────────┘    └─────────────────────────────┘ │  │
│  └─────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    传输管理层                                │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │              ConnectedRunnable                         │  │
│  │  ┌─────────────────┐    ┌─────────────────────────────┐ │  │
│  │  │   读取线程       │    │        写入操作            │ │  │
│  │  └─────────────────┘    └─────────────────────────────┘ │  │
│  └─────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                  蓝牙Socket层                                │
│  ┌─────────────────┐    ┌─────────────────────────────────┐  │
│  │   InputStream   │    │        OutputStream           │  │
│  └─────────────────┘    └─────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 协议格式详解

### 1. 音频数据流协议

#### 格式定义
```
音频数据包格式：
┌──────┬──────┬────────────────────────────────┐
│ 0xFF │ 0xFF │        音频数据载荷             │
│ (1B) │ (1B) │        (可变长度)              │
└──────┴──────┴────────────────────────────────┘
```

#### 特征识别
```java
// 音频数据识别逻辑
private boolean isVoiceData(byte[] data) {
    if (data != null && data.length > 1 && 
        (data[0] & 0xff) == 0xFF && (data[1] & 0xff) == 0xFF) {
        return true;
    }
    return false;
}
```

#### 实际数据示例
```
音频数据包示例：
FF FF 12 34 56 78 9A BC DE F0 11 22 33 44 55 66 77 88
│   │ └─────────────────────────────────────────────────┘
│   │                    音频数据载荷
│   └── 音频标识符 (0xFF)
└── 音频标识符 (0xFF)
```

### 2. 控制指令协议

#### 指令类型表

| 指令码 | 长度(字节) | 功能描述 | 数据格式示例 |
|--------|------------|----------|--------------|
| 0x07 | 5 | 通用控制指令 | `07 80 01 00 01` |
| 0x04 | 5 | 准备开始数据流 | `04 80 01 00 01` |
| 0x05 | 4 | 准备结束数据流 | `05 80 00 00` |
| 0x01 | 12 | 开始数据流标志 | `01 80 08 00 00 00 00 00 00 00 00 00` |
| 0x03 | 16 | 结束数据流标志 | `03 80 0C 00 00 00 00 00 00 00 00 00 00 00 00 00` |
| 0x00 | 可变 | 复杂控制指令 | `00 80 [LEN_L] [LEN_H] [DATA...]` |

#### 可变长度指令详解 (0x00)

```
可变长度指令格式：
┌──────┬──────┬────────┬────────┬────────────────────────┐
│ 0x00 │ 0x80 │ LEN_L  │ LEN_H  │      数据载荷           │
│ (1B) │ (1B) │  (1B)  │  (1B)  │    (LEN_L+LEN_H*256)  │
└──────┴──────┴────────┴────────┴────────────────────────┘
```

长度计算：
```java
// 小端序长度计算
int len = (datas[2] & 0xff) + (datas[3] << 8) + 4;
```

#### 实际指令示例

**配置文件获取指令**：
```
00 80 0E 00 0D 80 00 00 08 00 03 18 A0 02 00 0C 01 00
│  │  │  │  └─────────────────────────────────────────┘
│  │  │  │                   数据载荷 (14字节)
│  │  │  └── 高位长度 (0x00)
│  │  └── 低位长度 (0x0E = 14)
│  └── 固定标识 (0x80)
└── 指令码 (0x00)
```

**复杂配置指令**：
```
00 80 2F 00 0D 80 00 00 29 00 00 03 00 C0 00 00 06 01 00 01 00 00 00 00 0C 02 00 10 00 08 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
│  │  │  │  └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
│  │  │  │                                                    数据载荷 (47字节)
│  │  │  └── 高位长度 (0x00)
│  │  └── 低位长度 (0x2F = 47)
│  └── 固定标识 (0x80)
└── 指令码 (0x00)
```

## 数据接收处理流程

### 接收缓冲区管理

```java
// ConnectedRunnable中的数据接收
byte[] data = new byte[1944];  // 接收缓冲区大小
while (true) {
    int length = mRead.read(data);
    if (length > 0) {
        // 提取有效数据
        byte[] validData = ArrayUtil.extractBytes(data, 0, length);
        onReceive(validData);
    }
}
```

### 数据包重组机制

```mermaid
flowchart TD
    A[接收原始数据] --> B[与缓存数据合并]
    B --> C{数据长度检查}
    C -->|长度不足| D[缓存等待更多数据]
    C -->|长度充足| E[协议头识别]
    
    E --> F{数据类型?}
    F -->|0xFF 0xFF| G[音频数据处理]
    F -->|控制指令| H[指令长度验证]
    
    H --> I{数据完整?}
    I -->|否| D
    I -->|是| J[提取完整包]
    
    J --> K[更新缓存]
    K --> L{还有剩余数据?}
    L -->|是| M[处理剩余数据]
    L -->|否| N[清空缓存]
    
    G --> O[直接传递音频数据]
    M --> B
    N --> P[返回完整数据包]
    O --> P
```

### 缓存管理详解

#### 数据拼接逻辑
```java
private byte[] appendData(byte[] data) {
    if (waitMoreDatas != null) {
        // 拼接新数据到缓存数据
        byte[] dstData = new byte[waitMoreDatas.length + data.length];
        System.arraycopy(waitMoreDatas, 0, dstData, 0, waitMoreDatas.length);
        System.arraycopy(data, 0, dstData, waitMoreDatas.length, data.length);
        return dstData;
    } else {
        return data;
    }
}
```

#### 数据提取和剩余处理
```java
private byte[] getDataAndLeave(byte[] datas, int len) {
    if (datas == null || datas.length < len) {
        waitMoreDatas = null;
        return null;
    }
    
    // 提取完整数据包
    byte[] dstDatas = new byte[len];
    System.arraycopy(datas, 0, dstDatas, 0, len);
    
    if (datas.length == len) {
        // 数据刚好用完，清空缓存
        waitMoreDatas = null;
    } else {
        // 还有剩余数据，更新缓存
        int leaveDataLen = datas.length - len;
        byte[] leaveData = new byte[leaveDataLen];
        System.arraycopy(datas, len, leaveData, 0, leaveDataLen);
        waitMoreDatas = leaveData;
    }
    
    return dstDatas;
}
```

## 数据发送处理流程

### 发送接口
```java
// SppConnector发送接口
public boolean write(byte[] data) {
    if (mConnectedRunnable != null) {
        return mConnectedRunnable.write(data);
    }
    return false;
}

// ConnectedRunnable实际发送
public boolean write(byte[] data) {
    try {
        mWrite.write(data);
        return true;
    } catch (IOException e) {
        e.printStackTrace();
        onConnectionStateChanged(false);
    }
    return false;
}
```

### 发送数据格式

#### 音频数据发送
```java
// 发送音频数据示例
byte[] audioHeader = {(byte)0xFF, (byte)0xFF};
byte[] audioData = getAudioSamples(); // 获取音频样本
byte[] packet = new byte[audioHeader.length + audioData.length];
System.arraycopy(audioHeader, 0, packet, 0, audioHeader.length);
System.arraycopy(audioData, 0, packet, audioHeader.length, audioData.length);
connector.write(packet);
```

#### 控制指令发送
```java
// 发送开始数据流指令
byte[] startCommand = {0x04, (byte)0x80, 0x01, 0x00, 0x01};
connector.write(startCommand);

// 发送结束数据流指令
byte[] stopCommand = {0x05, (byte)0x80, 0x00, 0x00};
connector.write(stopCommand);
```

## 性能特性

### 缓冲区配置
- **接收缓冲区**：1944字节，适合音频数据的实时传输
- **数据包大小**：支持可变长度，最大理论长度65535字节
- **缓存机制**：智能数据包重组，避免数据丢失

### 传输性能
- **实时性**：支持音频数据的实时双向传输
- **可靠性**：通过数据包重组确保数据完整性
- **效率**：最小化内存拷贝，优化数据处理流程

### 错误处理
- **连接异常**：自动触发重连机制
- **数据异常**：丢弃不完整或格式错误的数据包
- **缓存溢出**：自动清理异常缓存数据

## 使用示例

### 完整的数据交互示例
```java
public class AudioDataHandler implements ConnectCallback {
    private SppConnector connector;
    
    public void initialize() {
        connector = SppConnector.getConnector();
        connector.addConnectCallback(this);
    }
    
    @Override
    public void onConnectionStateChanged(boolean connected) {
        if (connected) {
            // 连接成功，发送开始指令
            sendStartCommand();
        }
    }
    
    @Override
    public void onReceive(UUID uuid, byte[] data) {
        if (isAudioData(data)) {
            // 处理音频数据
            handleAudioData(data);
        } else {
            // 处理控制指令
            handleControlCommand(data);
        }
    }
    
    private void sendStartCommand() {
        byte[] command = {0x04, (byte)0x80, 0x01, 0x00, 0x01};
        connector.write(command);
    }
    
    private void sendAudioData(byte[] audioSamples) {
        byte[] packet = new byte[audioSamples.length + 2];
        packet[0] = (byte)0xFF;
        packet[1] = (byte)0xFF;
        System.arraycopy(audioSamples, 0, packet, 2, audioSamples.length);
        connector.write(packet);
    }
    
    private boolean isAudioData(byte[] data) {
        return data.length > 2 && 
               (data[0] & 0xFF) == 0xFF && 
               (data[1] & 0xFF) == 0xFF;
    }
    
    private void handleAudioData(byte[] data) {
        // 提取音频数据（跳过前两个字节的标识符）
        byte[] audioData = new byte[data.length - 2];
        System.arraycopy(data, 2, audioData, 0, audioData.length);
        
        // 处理音频数据
        processAudioSamples(audioData);
    }
    
    private void handleControlCommand(byte[] data) {
        if (data.length > 0) {
            int commandType = data[0] & 0xFF;
            switch (commandType) {
                case 0x07:
                    // 处理通用控制指令
                    break;
                case 0x04:
                    // 处理开始数据流指令
                    break;
                case 0x05:
                    // 处理结束数据流指令
                    break;
                // 其他指令处理...
            }
        }
    }
}
```

## 数据交互时序分析

### 典型音频传输会话

```mermaid
sequenceDiagram
    participant App as 应用层
    participant Connector as SppConnector
    participant Remote as 远程设备

    Note over App,Remote: 连接建立阶段
    App->>Connector: connect(device)
    Connector->>Remote: 建立SPP连接
    Remote-->>Connector: 连接确认
    Connector-->>App: onConnectionStateChanged(true)

    Note over App,Remote: 数据流准备阶段
    App->>Connector: write([0x04,0x80,0x01,0x00,0x01])
    Connector->>Remote: 发送开始数据流指令
    Remote-->>Connector: 确认指令
    Connector-->>App: onReceive(确认数据)

    Note over App,Remote: 音频数据传输阶段
    loop 音频数据传输
        App->>Connector: write([0xFF,0xFF,音频数据])
        Connector->>Remote: 发送音频包
        Remote->>Connector: 发送音频包
        Connector->>App: onReceive([0xFF,0xFF,音频数据])
    end

    Note over App,Remote: 数据流结束阶段
    App->>Connector: write([0x05,0x80,0x00,0x00])
    Connector->>Remote: 发送结束数据流指令
    Remote-->>Connector: 确认指令
    Connector-->>App: onReceive(确认数据)
```

### 数据包分片重组示例

```mermaid
sequenceDiagram
    participant Socket as BluetoothSocket
    participant Reader as ConnectedRunnable
    participant Helper as SppMessageHelper
    participant App as 应用层

    Note over Socket,App: 大数据包分片接收
    Socket->>Reader: read() 返回部分数据
    Reader->>Helper: checkDataEnoughAndRetArray(部分数据)
    Helper->>Helper: 缓存数据，等待更多
    Helper-->>Reader: 返回null

    Socket->>Reader: read() 返回剩余数据
    Reader->>Helper: checkDataEnoughAndRetArray(剩余数据)
    Helper->>Helper: 拼接数据，解析完整包
    Helper-->>Reader: 返回完整数据包数组
    Reader->>App: onReceive(完整数据)
```

## 协议扩展机制

### 自定义协议支持

框架支持通过扩展SppMessageHelper来实现自定义协议：

```java
public class CustomProtocolHelper extends SppMessageHelper {

    // 自定义协议标识
    private static final byte CUSTOM_PROTOCOL_HEADER = (byte)0xAA;

    @Override
    public byte[][] checkDataEnoughAndRetArray(byte[] data) {
        // 首先检查是否为自定义协议
        if (isCustomProtocol(data)) {
            return parseCustomProtocol(data);
        }

        // 否则使用默认协议解析
        return super.checkDataEnoughAndRetArray(data);
    }

    private boolean isCustomProtocol(byte[] data) {
        return data != null && data.length > 0 &&
               (data[0] & 0xFF) == CUSTOM_PROTOCOL_HEADER;
    }

    private byte[][] parseCustomProtocol(byte[] data) {
        // 实现自定义协议解析逻辑
        // 例如：AA [LEN] [DATA...]
        if (data.length < 2) {
            return null; // 数据不足
        }

        int expectedLength = (data[1] & 0xFF) + 2; // 头部2字节 + 数据长度
        if (data.length < expectedLength) {
            return null; // 等待更多数据
        }

        // 提取完整数据包
        byte[] packet = new byte[expectedLength];
        System.arraycopy(data, 0, packet, 0, expectedLength);

        return new byte[][]{packet};
    }
}
```

### 协议版本管理

```java
public class ProtocolVersionManager {
    private static final int PROTOCOL_V1 = 1;
    private static final int PROTOCOL_V2 = 2;

    private int currentVersion = PROTOCOL_V1;

    public void negotiateProtocolVersion(byte[] versionData) {
        // 协议版本协商逻辑
        if (versionData.length >= 4 &&
            versionData[0] == 0x00 && versionData[1] == (byte)0x80) {

            int requestedVersion = versionData[4] & 0xFF;
            if (requestedVersion == PROTOCOL_V2 && supportsV2()) {
                currentVersion = PROTOCOL_V2;
                sendVersionConfirmation(PROTOCOL_V2);
            } else {
                currentVersion = PROTOCOL_V1;
                sendVersionConfirmation(PROTOCOL_V1);
            }
        }
    }

    private boolean supportsV2() {
        // 检查是否支持协议V2
        return true;
    }

    private void sendVersionConfirmation(int version) {
        byte[] confirmation = {0x00, (byte)0x80, 0x01, 0x00, (byte)version};
        SppConnector.getConnector().write(confirmation);
    }
}
```

## 性能优化策略

### 1. 内存优化

```java
public class OptimizedDataHandler {
    // 使用对象池减少GC压力
    private final Queue<byte[]> bufferPool = new ConcurrentLinkedQueue<>();
    private static final int BUFFER_SIZE = 1944;
    private static final int MAX_POOL_SIZE = 10;

    public byte[] getBuffer() {
        byte[] buffer = bufferPool.poll();
        if (buffer == null) {
            buffer = new byte[BUFFER_SIZE];
        }
        return buffer;
    }

    public void returnBuffer(byte[] buffer) {
        if (buffer != null && buffer.length == BUFFER_SIZE &&
            bufferPool.size() < MAX_POOL_SIZE) {
            bufferPool.offer(buffer);
        }
    }
}
```

### 2. 数据压缩

```java
public class CompressedAudioHandler {

    public byte[] compressAudioData(byte[] rawAudio) {
        // 实现音频数据压缩
        // 例如使用简单的差分编码
        if (rawAudio.length < 2) return rawAudio;

        byte[] compressed = new byte[rawAudio.length];
        compressed[0] = rawAudio[0]; // 第一个样本不压缩

        for (int i = 1; i < rawAudio.length; i++) {
            // 存储与前一个样本的差值
            compressed[i] = (byte)(rawAudio[i] - rawAudio[i-1]);
        }

        return compressed;
    }

    public byte[] decompressAudioData(byte[] compressed) {
        // 实现音频数据解压缩
        if (compressed.length < 2) return compressed;

        byte[] decompressed = new byte[compressed.length];
        decompressed[0] = compressed[0]; // 第一个样本直接复制

        for (int i = 1; i < compressed.length; i++) {
            // 恢复原始样本值
            decompressed[i] = (byte)(decompressed[i-1] + compressed[i]);
        }

        return decompressed;
    }
}
```

### 3. 流量控制

```java
public class FlowControlManager {
    private static final int MAX_PENDING_PACKETS = 5;
    private final AtomicInteger pendingPackets = new AtomicInteger(0);
    private final Object flowControlLock = new Object();

    public boolean canSendData() {
        return pendingPackets.get() < MAX_PENDING_PACKETS;
    }

    public void onDataSent() {
        pendingPackets.incrementAndGet();
    }

    public void onDataAcknowledged() {
        synchronized (flowControlLock) {
            pendingPackets.decrementAndGet();
            flowControlLock.notifyAll();
        }
    }

    public void waitForFlowControl() throws InterruptedException {
        synchronized (flowControlLock) {
            while (!canSendData()) {
                flowControlLock.wait(1000); // 最多等待1秒
            }
        }
    }
}
```

## 调试和监控

### 数据包监控

```java
public class PacketMonitor {
    private long totalBytesSent = 0;
    private long totalBytesReceived = 0;
    private long audioPacketsSent = 0;
    private long audioPacketsReceived = 0;
    private long controlPacketsSent = 0;
    private long controlPacketsReceived = 0;

    public void onPacketSent(byte[] data) {
        totalBytesSent += data.length;

        if (isAudioPacket(data)) {
            audioPacketsSent++;
        } else {
            controlPacketsSent++;
        }

        logPacket("SENT", data);
    }

    public void onPacketReceived(byte[] data) {
        totalBytesReceived += data.length;

        if (isAudioPacket(data)) {
            audioPacketsReceived++;
        } else {
            controlPacketsReceived++;
        }

        logPacket("RECV", data);
    }

    private boolean isAudioPacket(byte[] data) {
        return data.length > 2 &&
               (data[0] & 0xFF) == 0xFF &&
               (data[1] & 0xFF) == 0xFF;
    }

    private void logPacket(String direction, byte[] data) {
        if (BuildConfig.DEBUG) {
            String hex = ArrayUtil.toHex(data);
            Log.d("PacketMonitor", String.format("%s: %s (%d bytes)",
                  direction, hex, data.length));
        }
    }

    public String getStatistics() {
        return String.format(
            "Statistics:\n" +
            "Total Sent: %d bytes (%d audio, %d control)\n" +
            "Total Received: %d bytes (%d audio, %d control)",
            totalBytesSent, audioPacketsSent, controlPacketsSent,
            totalBytesReceived, audioPacketsReceived, controlPacketsReceived
        );
    }
}
```

### 性能分析

```java
public class PerformanceAnalyzer {
    private final Map<String, Long> operationTimes = new ConcurrentHashMap<>();
    private final Map<String, Integer> operationCounts = new ConcurrentHashMap<>();

    public void startOperation(String operation) {
        operationTimes.put(operation + "_start", System.nanoTime());
    }

    public void endOperation(String operation) {
        long startTime = operationTimes.remove(operation + "_start");
        long duration = System.nanoTime() - startTime;

        operationTimes.merge(operation + "_total", duration, Long::sum);
        operationCounts.merge(operation, 1, Integer::sum);

        // 记录超时操作
        if (duration > 10_000_000) { // 10ms
            Log.w("Performance", String.format("%s took %d ms",
                  operation, duration / 1_000_000));
        }
    }

    public void printAnalysis() {
        for (String operation : operationCounts.keySet()) {
            long totalTime = operationTimes.get(operation + "_total");
            int count = operationCounts.get(operation);
            long avgTime = totalTime / count;

            Log.i("Performance", String.format(
                "%s: avg=%d μs, count=%d, total=%d ms",
                operation, avgTime / 1000, count, totalTime / 1_000_000
            ));
        }
    }
}
```

## 故障排除指南

### 常见数据交互问题

#### 1. 数据包丢失
**症状**：接收到的数据不完整或乱序
**原因**：
- 蓝牙信号不稳定
- 缓冲区溢出
- 协议解析错误

**解决方案**：
```java
// 增加数据包序号验证
public class SequenceValidator {
    private int expectedSequence = 0;

    public boolean validateSequence(byte[] data) {
        if (data.length < 3) return false;

        int sequence = data[2] & 0xFF;
        if (sequence == expectedSequence) {
            expectedSequence = (expectedSequence + 1) % 256;
            return true;
        } else {
            Log.w("Sequence", "Expected " + expectedSequence + ", got " + sequence);
            expectedSequence = (sequence + 1) % 256; // 重新同步
            return false;
        }
    }
}
```

#### 2. 音频数据延迟
**症状**：音频播放有明显延迟
**原因**：
- 数据处理时间过长
- 缓冲区积压
- 线程调度问题

**解决方案**：
```java
// 优化音频数据处理
public class LowLatencyAudioHandler {
    private final ExecutorService audioProcessor =
        Executors.newSingleThreadExecutor(r -> {
            Thread t = new Thread(r, "AudioProcessor");
            t.setPriority(Thread.MAX_PRIORITY);
            return t;
        });

    public void processAudioData(byte[] audioData) {
        audioProcessor.execute(() -> {
            // 高优先级处理音频数据
            handleAudioDataImmediate(audioData);
        });
    }
}
```

#### 3. 内存泄漏
**症状**：长时间运行后内存持续增长
**原因**：
- 缓存数据未及时清理
- 回调引用未释放
- 线程未正确停止

**解决方案**：
```java
// 定期清理和监控
public class MemoryManager {
    private final ScheduledExecutorService cleaner =
        Executors.newSingleThreadScheduledExecutor();

    public void startMemoryMonitoring() {
        cleaner.scheduleAtFixedRate(() -> {
            // 清理过期缓存
            SppMessageHelper.getInstant().clearSppData();

            // 强制GC（仅调试时使用）
            if (BuildConfig.DEBUG) {
                System.gc();
                logMemoryUsage();
            }
        }, 30, 30, TimeUnit.SECONDS);
    }

    private void logMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long used = runtime.totalMemory() - runtime.freeMemory();
        Log.d("Memory", "Used: " + (used / 1024 / 1024) + " MB");
    }
}
```

## 总结

BES蓝牙框架的数据交互机制具有以下特点：

### 核心优势
1. **协议灵活性**：支持音频流和控制指令的混合传输
2. **数据完整性**：通过智能缓存和重组确保数据包完整
3. **实时性能**：优化的缓冲区和线程模型支持实时音频传输
4. **扩展性**：支持自定义协议和版本管理

### 技术特色
1. **智能解析**：自动识别不同类型的数据包
2. **内存优化**：对象池和缓存管理减少GC压力
3. **错误恢复**：完善的异常处理和重连机制
4. **性能监控**：内置的调试和性能分析工具

### 应用场景
- 蓝牙音频设备通信
- 实时音频流传输
- 设备控制和配置
- 双向数据交换

这个数据交互框架为开发者提供了一个稳定、高效、可扩展的蓝牙通信解决方案，特别适用于需要实时音频传输的应用场景。
