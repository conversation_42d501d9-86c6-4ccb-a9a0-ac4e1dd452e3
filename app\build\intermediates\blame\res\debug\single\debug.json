[{"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_list_pressed_holo_dark.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_list_pressed_holo_dark.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_test_reflow_chipgroup.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\test_reflow_chipgroup.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_bottom_nav_item_tint.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_bottom_nav_item_tint.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_text_select_handle_left_mtrl_dark.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_text_select_handle_left_mtrl_dark.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_mtrl_tabs_default_indicator.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable\\mtrl_tabs_default_indicator.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_mtrl_on_surface_emphasis_medium.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color-v23\\mtrl_on_surface_emphasis_medium.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\interpolator_btn_checkbox_checked_mtrl_animation_interpolator_0.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\interpolator\\btn_checkbox_checked_mtrl_animation_interpolator_0.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_menu_hardkey_panel_mtrl_mult.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_tabs_icon_color_selector_colored.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_tabs_icon_color_selector_colored.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_ic_menu_share_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_ic_menu_share_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_mtrl_on_surface_emphasis_high_type.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color-v23\\mtrl_on_surface_emphasis_high_type.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_list_selector_holo_light.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_list_selector_holo_light.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\interpolator_btn_radio_to_on_mtrl_animation_interpolator_0.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\interpolator\\btn_radio_to_on_mtrl_animation_interpolator_0.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_ic_star_black_48dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_ic_star_black_48dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_slide_in_bottom.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\abc_slide_in_bottom.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_ic_star_half_black_16dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_ic_star_half_black_16dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_ic_star_half_black_36dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_ic_star_half_black_36dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_btn_radio_off_mtrl.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\btn_radio_off_mtrl.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_list_divider_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_list_divider_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_btn_checkbox_checked_mtrl.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\btn_checkbox_checked_mtrl.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_act_scan.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\layout\\act_scan.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_list_focused_holo.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_list_focused_holo.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_tooltip_enter.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\abc_tooltip_enter.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_ic_star_half_black_36dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_ic_star_half_black_36dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_btn_radio_to_off_mtrl_dot_group_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\btn_radio_to_off_mtrl_dot_group_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_list_selector_background_transition_holo_dark.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_list_selector_background_transition_holo_dark.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_material_alert_dialog.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\material_alert_dialog.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_btn_checkbox_to_unchecked_check_path_merged_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\btn_checkbox_to_unchecked_check_path_merged_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-v23_abc_control_background_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-v23\\abc_control_background_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-mdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-mdpi-v17\\abc_spinner_mtrl_am_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_mtrl_layout_snackbar_include.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\mtrl_layout_snackbar_include.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_action_mode_close_item_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_action_mode_close_item_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_ic_menu_share_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_ic_menu_share_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v11_ic_stat_search.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-mdpi-v11\\ic_stat_search.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_notification_bg_low_normal.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-xhdpi-v4\\notification_bg_low_normal.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_btn_radio_to_on_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_mtrl_on_primary_emphasis_medium.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color-v23\\mtrl_on_primary_emphasis_medium.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_abc_tint_seek_thumb.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color-v23\\abc_tint_seek_thumb.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_ic_mtrl_chip_close_circle.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable\\ic_mtrl_chip_close_circle.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_design_navigation_menu_item.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\design_navigation_menu_item.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-v21_mtrl_popupmenu_background.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-v21\\mtrl_popupmenu_background.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim-v21_mtrl_bottom_sheet_slide_out.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\anim-v21\\mtrl_bottom_sheet_slide_out.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_list_selector_disabled_holo_light.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_list_selector_disabled_holo_light.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v11_ic_stat_microphone.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xxxhdpi-v11\\ic_stat_microphone.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_btn_check_to_on_mtrl_015.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-v21_design_password_eye.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-v21\\design_password_eye.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_scrubber_primary_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_textfield_default_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_textfield_default_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_ic_star_half_black_48dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_ic_star_half_black_48dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v11_ic_stat_microphone.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-mdpi-v11\\ic_stat_microphone.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v11_ic_stat_search.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xxxhdpi-v11\\ic_stat_search.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_slide_in_top.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\abc_slide_in_top.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_cab_background_top_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_cab_background_top_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_ic_star_black_36dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_ic_star_black_36dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\menu_main.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\menu\\main.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_text_select_handle_left_mtrl_light.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_text_select_handle_left_mtrl_light.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_btn_check_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_btn_check_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_text_select_handle_left_mtrl_light.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_text_select_handle_left_mtrl_light.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_scrubber_control_off_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_spinner_mtrl_am_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_item_action_fragment.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\layout\\item_action_fragment.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-watch-v20_abc_dialog_material_background.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-watch-v20\\abc_dialog_material_background.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_btn_text_color_selector.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_btn_text_color_selector.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_scrubber_control_off_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_list_focused_holo.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_list_focused_holo.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_notification_bg_low_normal.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-mdpi-v4\\notification_bg_low_normal.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_tabs_ripple_color.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_tabs_ripple_color.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_ic_arrow_drop_right_black_24dp.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_ic_arrow_drop_right_black_24dp.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_btn_checkbox_to_unchecked_icon_null_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\btn_checkbox_to_unchecked_icon_null_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_textfield_activated_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_list_focused_holo.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_list_focused_holo.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_cascading_menu_item_layout.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_cascading_menu_item_layout.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_btn_radio_to_off_mtrl_ring_outer_path_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\btn_radio_to_off_mtrl_ring_outer_path_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_list_selector_background_transition_holo_light.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_list_selector_background_transition_holo_light.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_text_select_handle_middle_mtrl_light.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_text_select_handle_middle_mtrl_light.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_list_selector_disabled_holo_light.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_list_selector_disabled_holo_light.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v11_ic_stat_microphone.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-hdpi-v11\\ic_stat_microphone.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_textfield_activated_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_design_layout_snackbar_include.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\design_layout_snackbar_include.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_ic_star_half_black_16dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_ic_star_half_black_16dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_spinner_mtrl_am_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_btn_radio_to_on_mtrl_015.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_ic_star_half_black_16dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_ic_star_half_black_16dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_background_cache_hint_selector_material_light.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color\\abc_background_cache_hint_selector_material_light.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_textfield_search_activated_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_spinner_textfield_background_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_spinner_textfield_background_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v11_ic_stat_gear.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-mdpi-v11\\ic_stat_gear.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_ic_menu_selectall_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_ic_menu_selectall_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_notify_panel_notification_icon_bg.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-xhdpi-v4\\notify_panel_notification_icon_bg.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_btn_check_to_on_mtrl_015.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_mtrl_dark_on_surface_emphasis_medium.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color-v23\\mtrl_dark_on_surface_emphasis_medium.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout-watch-v20_abc_alert_dialog_title_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout-watch-v20\\abc_alert_dialog_title_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_secondary_text_material_dark.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color\\abc_secondary_text_material_dark.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout-v21_notification_template_icon_group.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\layout-v21\\notification_template_icon_group.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_switch_thumb_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_switch_thumb_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_activity_chooser_view.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_activity_chooser_view.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_mtrl_dark_on_surface_disabled.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color-v23\\mtrl_dark_on_surface_disabled.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_chip_close_icon_tint.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_chip_close_icon_tint.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_abc_btn_colored_text_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color-v23\\abc_btn_colored_text_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_mtrl_dark_on_primary_emphasis_medium.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color-v23\\mtrl_dark_on_primary_emphasis_medium.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_design_ic_visibility_off.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-hdpi-v4\\design_ic_visibility_off.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v11_ic_stat_microphone.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xhdpi-v11\\ic_stat_microphone.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-anydpi-v21_design_ic_visibility.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-anydpi-v21\\design_ic_visibility.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-hdpi-v17_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-hdpi-v17\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xxxhdpi-v17_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-xxxhdpi-v17\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v11_ic_stat_gear.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-hdpi-v11\\ic_stat_gear.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_ic_star_half_black_48dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_ic_star_half_black_48dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_popup_menu_item_layout.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_popup_menu_item_layout.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_design_snackbar_background.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable\\design_snackbar_background.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_select_dialog_item_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\select_dialog_item_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_side_nav_bar.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable\\side_nav_bar.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_scrubber_primary_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_action_bar_up_container.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_action_bar_up_container.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_list_pressed_holo_light.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_list_pressed_holo_light.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-v21_avd_hide_password.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-v21\\avd_hide_password.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_tooltip_exit.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\abc_tooltip_exit.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_popup_exit.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\abc_popup_exit.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\animator_design_fab_show_motion_spec.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\animator\\design_fab_show_motion_spec.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-mdpi_ic_launcher.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-anydpi-v21_design_ic_visibility_off.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-anydpi-v21\\design_ic_visibility_off.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_text_select_handle_right_mtrl_dark.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_text_select_handle_right_mtrl_dark.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-v21_abc_list_divider_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-v21\\abc_list_divider_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_tooltip_frame_dark.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\tooltip_frame_dark.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim-v21_design_bottom_sheet_slide_out.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\anim-v21\\design_bottom_sheet_slide_out.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\raw_code_prerecorded.txt.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\raw\\code_prerecorded.txt"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v11_ic_stat_pause.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xhdpi-v11\\ic_stat_pause.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_text_select_handle_right_mtrl_light.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_text_select_handle_right_mtrl_light.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_textfield_search_default_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_scrubber_primary_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_action_bar_title_item.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_action_bar_title_item.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_ic_star_black_16dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_ic_star_black_16dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\raw_bdspeech_recognition_start.mp3.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\raw\\bdspeech_recognition_start.mp3"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_design_bottom_sheet_dialog.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\design_bottom_sheet_dialog.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_btn_bg.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable\\btn_bg.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\raw_code_base.txt.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\raw\\code_base.txt"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v11_ic_stat_search.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xxhdpi-v11\\ic_stat_search.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_list_pressed_holo_light.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_list_pressed_holo_light.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_tabs_legacy_text_color_selector.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_tabs_legacy_text_color_selector.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_text_select_handle_middle_mtrl_light.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_text_select_handle_middle_mtrl_light.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_secondary_text_material_light.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color\\abc_secondary_text_material_light.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_ab_share_pack_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout-v26_abc_screen_toolbar.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout-v26\\abc_screen_toolbar.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-v21_design_bottom_navigation_item_background.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-v21\\design_bottom_navigation_item_background.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_text_select_handle_left_mtrl_dark.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_text_select_handle_left_mtrl_dark.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_background_item_action.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable\\background_item_action.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_hint_foreground_material_light.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color\\abc_hint_foreground_material_light.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_notification_template_part_time.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\layout\\notification_template_part_time.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_textfield_activated_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_filled_background_color.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_filled_background_color.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_textfield_default_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_textfield_default_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_chip_surface_color.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_chip_surface_color.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_text_select_handle_middle_mtrl_dark.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_text_select_handle_middle_mtrl_dark.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_ic_commit_search_api_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_error.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_error.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_notify_panel_notification_icon_bg.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-mdpi-v4\\notify_panel_notification_icon_bg.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_item_background_holo_light.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_item_background_holo_light.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\animator_mtrl_fab_transformation_sheet_expand_spec.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\animator\\mtrl_fab_transformation_sheet_expand_spec.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_chip_ripple_color.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_chip_ripple_color.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_btn_radio_to_on_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_ic_mtrl_chip_checked_black.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable\\ic_mtrl_chip_checked_black.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_ic_star_half_black_48dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_ic_star_half_black_48dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-mdpi-v17_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-mdpi-v17\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_switch_track_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_slide_out_bottom.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\abc_slide_out_bottom.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_notification_bg_normal.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-xhdpi-v4\\notification_bg_normal.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_list_selector_disabled_holo_dark.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_list_selector_disabled_holo_dark.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v11_ic_stat_pause.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-mdpi-v11\\ic_stat_pause.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\interpolator_btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\interpolator\\btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xxhdpi-v17_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-xxhdpi-v17\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout-v21_notification_action_tombstone.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\layout-v21\\notification_action_tombstone.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_abc_tint_default.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color-v23\\abc_tint_default.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_ic_star_black_16dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_ic_star_black_16dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_scrubber_primary_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_ic_search_api_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_ic_search_api_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_list_selector_disabled_holo_dark.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_list_selector_disabled_holo_dark.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_list_pressed_holo_dark.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_list_pressed_holo_dark.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_list_selector_disabled_holo_light.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_list_selector_disabled_holo_light.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_primary_text_disable_only_material_light.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color\\abc_primary_text_disable_only_material_light.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_ic_action_paste.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-mdpi\\ic_action_paste.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_notification_bg_normal.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-mdpi-v4\\notification_bg_normal.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_ic_menu_selectall_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_ic_menu_selectall_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_design_ic_visibility.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-hdpi-v4\\design_ic_visibility.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_tooltip_frame_light.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\tooltip_frame_light.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_cab_background_top_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_btn_radio_to_on_mtrl_dot_group_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\btn_radio_to_on_mtrl_dot_group_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_cab_background_internal_bg.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_cab_background_internal_bg.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\animator-v21_design_appbar_state_list_animator.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\animator-v21\\design_appbar_state_list_animator.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_btn_check_material_anim.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_btn_check_material_anim.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_ab_share_pack_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_ic_star_half_black_36dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_ic_star_half_black_36dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout-watch-v20_abc_alert_dialog_button_bar_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout-watch-v20\\abc_alert_dialog_button_bar_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_list_pressed_holo_light.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_list_pressed_holo_light.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_select_dialog_multichoice_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\select_dialog_multichoice_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-mdpi-v17_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-mdpi-v17\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xxhdpi_signal_level5.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\mipmap-xxhdpi\\signal_level5.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_btn_bg_color_selector.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_btn_bg_color_selector.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_notification_bg_low_pressed.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-xhdpi-v4\\notification_bg_low_pressed.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_cab_background_top_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_btn_checkbox_to_checked_box_outer_merged_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\btn_checkbox_to_checked_box_outer_merged_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-hdpi_ic_launcher.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_btn_radio_to_on_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_notification_bg_low_pressed.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-hdpi-v4\\notification_bg_low_pressed.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_textfield_search_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_textfield_search_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_mtrl_dark_on_primary_emphasis_high_type.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color-v23\\mtrl_dark_on_primary_emphasis_high_type.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_popup_background_mtrl_mult.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_popup_background_mtrl_mult.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xhdpi_ic_launcher.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\mipmap-xhdpi\\ic_launcher.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_alert_dialog_title_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_alert_dialog_title_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_textfield_search_default_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_list_menu_item_checkbox.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_list_menu_item_checkbox.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_design_navigation_item_subheader.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\design_navigation_item_subheader.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_list_longpressed_holo.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_list_longpressed_holo.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_notification_bg.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable\\notification_bg.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_ic_menu_paste_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_ic_menu_paste_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_fab_ripple_color.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_fab_ripple_color.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\interpolator_btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\interpolator\\btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_design_box_stroke_color.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\design_box_stroke_color.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_ic_star_half_black_16dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_ic_star_half_black_16dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_mtrl_dark_on_surface_emphasis_high_type.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color-v23\\mtrl_dark_on_surface_emphasis_high_type.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_chip_background_color.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_chip_background_color.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_popup_menu_header_item_layout.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_popup_menu_header_item_layout.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_screen_simple_overlay_action_mode.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_screen_simple_overlay_action_mode.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_ic_menu_share_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_ic_menu_share_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_text_select_handle_right_mtrl_light.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_text_select_handle_right_mtrl_light.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_primary_text_material_dark.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color\\abc_primary_text_material_dark.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_btn_checkbox_unchecked_to_checked_mtrl_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\btn_checkbox_unchecked_to_checked_mtrl_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim-v21_mtrl_bottom_sheet_slide_in.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\anim-v21\\mtrl_bottom_sheet_slide_in.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_btn_radio_to_on_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_textfield_search_activated_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_design_menu_item_action_area.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\design_menu_item_action_area.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\interpolator_mtrl_linear.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\interpolator\\mtrl_linear.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_navigation_empty_icon.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable\\navigation_empty_icon.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_popup_enter.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\abc_popup_enter.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_design_ic_visibility.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-xxhdpi-v4\\design_ic_visibility.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_text_select_handle_right_mtrl_dark.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_text_select_handle_right_mtrl_dark.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_textfield_search_default_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\raw_code_audio.txt.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\raw\\code_audio.txt"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v11_ic_stat_pause.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-hdpi-v11\\ic_stat_pause.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_btn_checkbox_to_unchecked_box_inner_merged_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\btn_checkbox_to_unchecked_box_inner_merged_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_btn_radio_on_to_off_mtrl_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\btn_radio_on_to_off_mtrl_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_text_select_handle_right_mtrl_dark.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_text_select_handle_right_mtrl_dark.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_custom_dialog.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\layout\\custom_dialog.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_ic_star_black_36dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_ic_star_black_36dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_mtrl_on_surface_disabled.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color-v23\\mtrl_on_surface_disabled.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_btn_check_to_on_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_ic_menu_selectall_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_ic_menu_selectall_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_btn_check_to_on_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_scrubber_track_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_mtrl_on_primary_emphasis_high_type.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color-v23\\mtrl_on_primary_emphasis_high_type.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_switch_thumb_material_dark.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color\\switch_thumb_material_dark.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_switch_track_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_design_ic_visibility_off.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-xxxhdpi-v4\\design_ic_visibility_off.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_ic_star_black_48dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_ic_star_black_48dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_select_dialog_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_select_dialog_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\animator_mtrl_btn_state_list_anim.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\animator\\mtrl_btn_state_list_anim.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_scrubber_track_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_btn_radio_to_on_mtrl_ring_outer_path_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\btn_radio_to_on_mtrl_ring_outer_path_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_btn_radio_to_on_mtrl_015.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_screen_simple.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_screen_simple.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_notification_bg_low.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable\\notification_bg_low.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_mtrl_dark_on_primary_disabled.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color-v23\\mtrl_dark_on_primary_disabled.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_abc_tint_edittext.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color-v23\\abc_tint_edittext.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_spinner_mtrl_am_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_design_navigation_item.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\design_navigation_item.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_text_select_handle_middle_mtrl_dark.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_text_select_handle_middle_mtrl_dark.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_text_select_handle_right_mtrl_light.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_text_select_handle_right_mtrl_light.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v11_ic_stat_gear.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xxxhdpi-v11\\ic_stat_gear.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\animator_mtrl_fab_hide_motion_spec.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\animator\\mtrl_fab_hide_motion_spec.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_text_select_handle_left_mtrl_dark.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_text_select_handle_left_mtrl_dark.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\interpolator_fast_out_slow_in.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\interpolator\\fast_out_slow_in.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_seekbar_tick_mark_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_seekbar_tick_mark_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_design_snackbar_in.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\anim\\design_snackbar_in.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_tabs_colored_ripple_color.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_tabs_colored_ripple_color.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_design_ic_visibility.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-mdpi-v4\\design_ic_visibility.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\interpolator_btn_checkbox_checked_mtrl_animation_interpolator_1.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\interpolator\\btn_checkbox_checked_mtrl_animation_interpolator_1.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_btn_check_to_on_mtrl_015.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_activity_main.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\layout\\activity_main.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout-v21_notification_template_custom_big.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\layout-v21\\notification_template_custom_big.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_screen_content_include.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_screen_content_include.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_ic_ab_back_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_ic_ab_back_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_background_cache_hint_selector_material_dark.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color\\abc_background_cache_hint_selector_material_dark.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\interpolator-v21_mtrl_linear_out_slow_in.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\interpolator-v21\\mtrl_linear_out_slow_in.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_notify_panel_notification_icon_bg.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-hdpi-v4\\notify_panel_notification_icon_bg.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_btn_checkbox_to_checked_icon_null_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\btn_checkbox_to_checked_icon_null_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_text_select_handle_middle_mtrl_light.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_text_select_handle_middle_mtrl_light.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_fragment_actions.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\layout\\fragment_actions.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_text_select_handle_left_mtrl_light.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_text_select_handle_left_mtrl_light.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_ic_menu_share_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_ic_menu_share_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_list_longpressed_holo.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_list_longpressed_holo.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_list_divider_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_list_divider_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_scrubber_track_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_progress_bg.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable\\progress_bg.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_list_selector_disabled_holo_light.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_list_selector_disabled_holo_light.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_btn_check_to_on_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_activity_chooser_view_list_item.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_activity_chooser_view_list_item.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_ic_voice_search_api_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_ic_voice_search_api_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v11_ic_stat_search.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xhdpi-v11\\ic_stat_search.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_ic_menu_paste_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_ic_menu_paste_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_mtrl_on_primary_disabled.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color-v23\\mtrl_on_primary_disabled.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout-sw600dp-v13_design_layout_snackbar.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout-sw600dp-v13\\design_layout_snackbar.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_hint_foreground_material_dark.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color\\abc_hint_foreground_material_dark.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_chip_text_color.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_chip_text_color.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_support_simple_spinner_dropdown_item.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\support_simple_spinner_dropdown_item.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_textfield_search_activated_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_switch_track_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-v21_abc_edit_text_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-v21\\abc_edit_text_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_btn_check_to_on_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_ic_star_black_48dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_ic_star_black_48dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_btn_borderless_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_btn_borderless_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_ab_share_pack_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v11_ic_stat_gear.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xxhdpi-v11\\ic_stat_gear.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_textfield_activated_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_logo.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xhdpi\\logo.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_notification_bg_normal_pressed.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-xhdpi-v4\\notification_bg_normal_pressed.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_notification_bg_normal_pressed.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-mdpi-v4\\notification_bg_normal_pressed.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_select_dialog_singlechoice_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\select_dialog_singlechoice_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_design_ic_visibility_off.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-xhdpi-v4\\design_ic_visibility_off.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_fade_out.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\abc_fade_out.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_list_pressed_holo_light.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_list_pressed_holo_light.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_shrink_fade_out_from_bottom.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\abc_shrink_fade_out_from_bottom.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-hdpi-v17_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-hdpi-v17\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_ic_menu_overflow_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_ic_menu_overflow_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_text_select_handle_left_mtrl_dark.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_text_select_handle_left_mtrl_dark.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_list_selector_disabled_holo_dark.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_list_selector_disabled_holo_dark.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_design_layout_tab_icon.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\design_layout_tab_icon.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_dialog_title_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_dialog_title_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_ratingbar_small_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_ratingbar_small_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_btn_radio_off_to_on_mtrl_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\btn_radio_off_to_on_mtrl_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_primary_text_material_light.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color\\abc_primary_text_material_light.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_btn_checkbox_checked_to_unchecked_mtrl_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\btn_checkbox_checked_to_unchecked_mtrl_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_ic_star_black_36dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_ic_star_black_36dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_list_menu_item_radio.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_list_menu_item_radio.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xxhdpi_signal_level4.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\mipmap-xxhdpi\\signal_level4.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_background_edittext.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable\\background_edittext.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_indicator_text_color.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_indicator_text_color.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_btn_radio_to_on_mtrl_015.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_cab_background_top_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_btn_radio_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_btn_radio_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_slide_from_right.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\anim\\slide_from_right.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_action_mode_bar.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_action_mode_bar.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_list_focused_holo.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_list_focused_holo.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_bottom_nav_colored_item_tint.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_bottom_nav_colored_item_tint.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_textfield_search_default_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_btn_radio_to_on_mtrl_ring_outer_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\btn_radio_to_on_mtrl_ring_outer_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout-v21_notification_action.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\layout-v21\\notification_action.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-v21_avd_show_password.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-v21\\avd_show_password.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_list_longpressed_holo.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_list_longpressed_holo.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_btn_radio_to_on_mtrl_015.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xhdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-xhdpi-v17\\abc_spinner_mtrl_am_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_cab_background_top_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-v21_abc_btn_colored_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-v21\\abc_btn_colored_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_primary_text_disable_only_material_dark.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color\\abc_primary_text_disable_only_material_dark.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_mtrl_layout_snackbar.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\mtrl_layout_snackbar.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_design_ic_visibility.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-xxxhdpi-v4\\design_ic_visibility.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_notification_bg_low_pressed.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-mdpi-v4\\notification_bg_low_pressed.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ic_action_paste.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-hdpi\\ic_action_paste.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_ic_menu_selectall_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_ic_menu_selectall_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_fade_in.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\abc_fade_in.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_btn_text_btn_ripple_color.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_btn_text_btn_ripple_color.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_design_text_input_password_icon.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\design_text_input_password_icon.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\animator_mtrl_fab_transformation_sheet_collapse_spec.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\animator\\mtrl_fab_transformation_sheet_collapse_spec.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_btn_radio_on_mtrl.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\btn_radio_on_mtrl.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_tooltip.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_tooltip.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_popup_background_mtrl_mult.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_popup_background_mtrl_mult.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_design_layout_tab_text.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\design_layout_tab_text.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_textfield_default_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_textfield_default_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_ic_star_black_36dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_ic_star_black_36dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_btn_radio_to_on_mtrl_015.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_design_fab_background.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable\\design_fab_background.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_item_background_holo_dark.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_item_background_holo_dark.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_btn_ripple_color.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_btn_ripple_color.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_btn_check_to_on_mtrl_015.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_menu_hardkey_panel_mtrl_mult.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_notification_bg_low_normal.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-hdpi-v4\\notification_bg_low_normal.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_btn_radio_to_off_mtrl_ring_outer_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\btn_radio_to_off_mtrl_ring_outer_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_tab_indicator_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_tab_indicator_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v11_ic_stat_gear.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xhdpi-v11\\ic_stat_gear.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_ic_commit_search_api_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_alert_dialog_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_alert_dialog_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_design_ic_visibility.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-xhdpi-v4\\design_ic_visibility.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xxhdpi_signal_level2.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\mipmap-xxhdpi\\signal_level2.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_action_menu_item_layout.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_action_menu_item_layout.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim-v21_design_bottom_sheet_slide_in.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\anim-v21\\design_bottom_sheet_slide_in.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_slide_to_left.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\anim\\slide_to_left.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_design_tint_password_toggle.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\design_tint_password_toggle.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_btn_check_to_on_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_ic_menu_share_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_ic_menu_share_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_text_select_handle_right_mtrl_dark.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_text_select_handle_right_mtrl_dark.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_design_error.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\design_error.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_design_navigation_menu.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\design_navigation_menu.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_ic_star_black_36dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_ic_star_black_36dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\animator_mtrl_btn_unelevated_state_list_anim.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\animator\\mtrl_btn_unelevated_state_list_anim.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_list_pressed_holo_dark.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_list_pressed_holo_dark.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_scrubber_control_off_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_design_ic_visibility_off.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-mdpi-v4\\design_ic_visibility_off.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_action_menu_layout.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_action_menu_layout.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xxhdpi_signal_level3.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\mipmap-xxhdpi\\signal_level3.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_spinner_mtrl_am_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_btn_default_mtrl_shape.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_btn_default_mtrl_shape.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_text_select_handle_middle_mtrl_dark.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_text_select_handle_middle_mtrl_dark.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_design_layout_snackbar.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\design_layout_snackbar.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_activity_filelist.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\layout\\activity_filelist.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_btn_check_to_on_mtrl_015.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_text_select_handle_middle_mtrl_dark.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_text_select_handle_middle_mtrl_dark.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_abc_tint_spinner.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color-v23\\abc_tint_spinner.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_list_menu_item_icon.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_list_menu_item_icon.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_ic_star_black_48dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_ic_star_black_48dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_btn_radio_to_on_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_ic_go_search_api_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_ic_go_search_api_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xhdpi-v17_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-xhdpi-v17\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_abc_tint_btn_checkable.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color-v23\\abc_tint_btn_checkable.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_ratingbar_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_ratingbar_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_material_alert_dialog_title.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\material_alert_dialog_title.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_design_ic_visibility_off.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable-xxhdpi-v4\\design_ic_visibility_off.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_ic_menu_selectall_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_ic_menu_selectall_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_textfield_default_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_textfield_default_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_ic_star_half_black_16dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_ic_star_half_black_16dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_ic_star_half_black_36dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_ic_star_half_black_36dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_design_navigation_item_header.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\design_navigation_item_header.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_btn_checkbox_unchecked_mtrl.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\btn_checkbox_unchecked_mtrl.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_btn_stroke_color_selector.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_btn_stroke_color_selector.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_grow_fade_in_from_bottom.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\abc_grow_fade_in_from_bottom.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_btn_checkbox_to_checked_box_inner_merged_animation.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\btn_checkbox_to_checked_box_inner_merged_animation.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_text_select_handle_left_mtrl_light.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_text_select_handle_left_mtrl_light.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_list_longpressed_holo.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_list_longpressed_holo.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\raw_code_text.txt.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\raw\\code_text.txt"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_app_bar_main.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\layout\\app_bar_main.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout-sw600dp-v13_mtrl_layout_snackbar.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout-sw600dp-v13\\mtrl_layout_snackbar.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-v21_notification_action_background.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-v21\\notification_action_background.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_ic_action_paste.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xhdpi\\ic_action_paste.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_popup_background_mtrl_mult.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_popup_background_mtrl_mult.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_text_select_handle_middle_mtrl_light.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_text_select_handle_middle_mtrl_light.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_search_dropdown_item_icons_2line.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_search_dropdown_item_icons_2line.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_ic_star_black_48dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_ic_star_black_48dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_notification_tile_bg.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable\\notification_tile_bg.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_notification_bg_normal.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-hdpi-v4\\notification_bg_normal.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_ic_menu_paste_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_ic_menu_paste_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_ic_star_black_16dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_ic_star_black_16dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_design_snackbar_out.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\anim\\design_snackbar_out.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_ic_star_half_black_36dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_ic_star_half_black_36dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_list_divider_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_list_divider_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_seekbar_thumb_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_seekbar_thumb_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xxxhdpi-v17_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-xxxhdpi-v17\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xhdpi-v17_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-xhdpi-v17\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_ic_star_half_black_48dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_ic_star_half_black_48dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_ic_clear_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_ic_clear_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_abc_btn_colored_borderless_text_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color-v23\\abc_btn_colored_borderless_text_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_text_select_handle_left_mtrl_light.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_text_select_handle_left_mtrl_light.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_tabs_icon_color_selector.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_tabs_icon_color_selector.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v11_ic_stat_pause.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xxhdpi-v11\\ic_stat_pause.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_abc_tint_switch_track.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color-v23\\abc_tint_switch_track.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_file_item.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\layout\\file_item.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xhdpi_logo.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\mipmap-xhdpi\\logo.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_search_view.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_search_view.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_list_pressed_holo_dark.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_list_pressed_holo_dark.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_vector_test.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_vector_test.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_list_selector_holo_dark.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_list_selector_holo_dark.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_menu_hardkey_panel_mtrl_mult.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_notification_template_part_chronometer.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\layout\\notification_template_part_chronometer.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_popup_background_mtrl_mult.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_popup_background_mtrl_mult.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-v21_abc_dialog_material_background.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-v21\\abc_dialog_material_background.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_scrubber_control_off_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\animator_mtrl_chip_state_list_anim.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\animator\\mtrl_chip_state_list_anim.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\animator_design_fab_hide_motion_spec.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\animator\\design_fab_hide_motion_spec.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_menu_hardkey_panel_mtrl_mult.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_switch_track_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xxhdpi_signal_level0.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\mipmap-xxhdpi\\signal_level0.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_button_search.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable\\button_search.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_ab_share_pack_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_text_select_handle_right_mtrl_light.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_text_select_handle_right_mtrl_light.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi-v4_abc_scrubber_track_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-mdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_list_selector_disabled_holo_dark.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_list_selector_disabled_holo_dark.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v11_ic_stat_pause.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xxxhdpi-v11\\ic_stat_pause.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_text_select_handle_right_mtrl_dark.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_text_select_handle_right_mtrl_dark.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-hdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-hdpi-v17\\abc_spinner_mtrl_am_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xxhdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-xxhdpi-v17\\abc_spinner_mtrl_am_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_ic_commit_search_api_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v11_ic_stat_search.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-hdpi-v11\\ic_stat_search.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_ic_star_black_16dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_ic_star_black_16dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_textfield_search_activated_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_spinner_mtrl_am_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_design_navigation_item_separator.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\design_navigation_item_separator.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_text_select_handle_left_mtrl_dark.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_text_select_handle_left_mtrl_dark.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_ratingbar_indicator_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_ratingbar_indicator_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_seekbar_track_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_seekbar_track_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\interpolator-v21_mtrl_fast_out_slow_in.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\interpolator-v21\\mtrl_fast_out_slow_in.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_search_url_text.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color\\abc_search_url_text.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_text_select_handle_right_mtrl_light.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_text_select_handle_right_mtrl_light.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\animator_mtrl_fab_show_motion_spec.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\animator\\mtrl_fab_show_motion_spec.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_slide_out_top.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\anim\\abc_slide_out_top.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_abc_color_highlight_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color-v23\\abc_color_highlight_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_text_cursor_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_text_cursor_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_switch_thumb_material_light.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\color\\switch_thumb_material_light.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_ic_commit_search_api_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_design_bottom_navigation_item.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\layout\\design_bottom_navigation_item.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_list_divider_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_list_divider_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_ic_star_half_black_48dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_ic_star_half_black_48dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi-v4_abc_ic_menu_paste_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xhdpi-v4\\abc_ic_menu_paste_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_alert_dialog_button_bar_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_alert_dialog_button_bar_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_expanded_menu_layout.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_expanded_menu_layout.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v11_ic_stat_microphone.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xxhdpi-v11\\ic_stat_microphone.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi-v4_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxhdpi-v4\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_text_btn_text_color_selector.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_text_btn_text_color_selector.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\color_mtrl_box_stroke_color.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\color\\mtrl_box_stroke_color.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\interpolator-v21_mtrl_fast_out_linear_in.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\interpolator-v21\\mtrl_fast_out_linear_in.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_abc_ic_menu_paste_mtrl_am_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-hdpi-v4\\abc_ic_menu_paste_mtrl_am_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xxhdpi-v17_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-xxhdpi-v17\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xxhdpi_signal_level1.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\mipmap-xxhdpi\\signal_level1.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_list_menu_item_layout.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\layout\\abc_list_menu_item_layout.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\layout_device_item.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\layout\\device_item.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi-v4_notification_bg_normal_pressed.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable-hdpi-v4\\notification_bg_normal_pressed.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_ic_star_black_16dp.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_ic_star_black_16dp.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-v21_abc_action_bar_item_background_material.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-v21\\abc_action_bar_item_background_material.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_switch_track_mtrl_alpha.9.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_notification_icon_background.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\core-1.1.0-alpha01.aar\\baf96cf68e23abfb63215a5f8f866694\\res\\drawable\\notification_icon_background.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_ic_action_paste.png.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\drawable-xxhdpi\\ic_action_paste.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\anim_slide_to_right.xml.flat", "source": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\src\\main\\res\\anim\\slide_to_right.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_ic_mtrl_chip_checked_circle.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\material-1.1.0-alpha02.aar\\d3853187af38bb9183fb92c02b540d96\\res\\drawable\\ic_mtrl_chip_checked_circle.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\interpolator_btn_radio_to_off_mtrl_animation_interpolator_0.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\interpolator\\btn_radio_to_off_mtrl_animation_interpolator_0.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-xxxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable_abc_btn_radio_material_anim.xml.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable\\abc_btn_radio_material_anim.xml"}, {"merged": "D:\\test_project\\Opus_spp_audio_dump_single_16000\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xxxhdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat", "source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-1\\files-1.1\\appcompat-1.1.0-alpha01.aar\\8ac0f9d07d9ee6ba6670901fa310d97e\\res\\drawable-ldrtl-xxxhdpi-v17\\abc_spinner_mtrl_am_alpha.9.png"}]