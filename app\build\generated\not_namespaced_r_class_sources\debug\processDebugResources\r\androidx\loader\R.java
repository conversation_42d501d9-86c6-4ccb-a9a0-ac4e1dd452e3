/* AUTO-GENERATED FILE.  DO NOT MODIFY.
 *
 * This class was automatically generated by the
 * gradle plugin from the resource data it found. It
 * should not be modified by hand.
 */
package androidx.loader;

public final class R {
    private R() {}

    public static final class attr {
        private attr() {}

        public static final int alpha = 0x7f03002b;
        public static final int font = 0x7f030100;
        public static final int fontProviderAuthority = 0x7f030102;
        public static final int fontProviderCerts = 0x7f030103;
        public static final int fontProviderFetchStrategy = 0x7f030104;
        public static final int fontProviderFetchTimeout = 0x7f030105;
        public static final int fontProviderPackage = 0x7f030106;
        public static final int fontProviderQuery = 0x7f030107;
        public static final int fontStyle = 0x7f030108;
        public static final int fontVariationSettings = 0x7f030109;
        public static final int fontWeight = 0x7f03010a;
        public static final int ttcIndex = 0x7f03020f;
    }
    public static final class color {
        private color() {}

        public static final int notification_action_color_filter = 0x7f05009a;
        public static final int notification_icon_bg_color = 0x7f05009b;
        public static final int ripple_material_light = 0x7f0500a5;
        public static final int secondary_text_default_material_light = 0x7f0500a7;
    }
    public static final class dimen {
        private dimen() {}

        public static final int compat_button_inset_horizontal_material = 0x7f060050;
        public static final int compat_button_inset_vertical_material = 0x7f060051;
        public static final int compat_button_padding_horizontal_material = 0x7f060052;
        public static final int compat_button_padding_vertical_material = 0x7f060053;
        public static final int compat_control_corner_material = 0x7f060054;
        public static final int compat_notification_large_icon_max_height = 0x7f060055;
        public static final int compat_notification_large_icon_max_width = 0x7f060056;
        public static final int notification_action_icon_size = 0x7f0600d6;
        public static final int notification_action_text_size = 0x7f0600d7;
        public static final int notification_big_circle_margin = 0x7f0600d8;
        public static final int notification_content_margin_start = 0x7f0600d9;
        public static final int notification_large_icon_height = 0x7f0600da;
        public static final int notification_large_icon_width = 0x7f0600db;
        public static final int notification_main_column_padding_top = 0x7f0600dc;
        public static final int notification_media_narrow_margin = 0x7f0600dd;
        public static final int notification_right_icon_size = 0x7f0600de;
        public static final int notification_right_side_padding_top = 0x7f0600df;
        public static final int notification_small_icon_background_padding = 0x7f0600e0;
        public static final int notification_small_icon_size_as_large = 0x7f0600e1;
        public static final int notification_subtext_size = 0x7f0600e2;
        public static final int notification_top_pad = 0x7f0600e3;
        public static final int notification_top_pad_large_text = 0x7f0600e4;
    }
    public static final class drawable {
        private drawable() {}

        public static final int notification_action_background = 0x7f07007c;
        public static final int notification_bg = 0x7f07007d;
        public static final int notification_bg_low = 0x7f07007e;
        public static final int notification_bg_low_normal = 0x7f07007f;
        public static final int notification_bg_low_pressed = 0x7f070080;
        public static final int notification_bg_normal = 0x7f070081;
        public static final int notification_bg_normal_pressed = 0x7f070082;
        public static final int notification_icon_background = 0x7f070083;
        public static final int notification_template_icon_bg = 0x7f070084;
        public static final int notification_template_icon_low_bg = 0x7f070085;
        public static final int notification_tile_bg = 0x7f070086;
        public static final int notify_panel_notification_icon_bg = 0x7f070087;
    }
    public static final class id {
        private id() {}

        public static final int action_container = 0x7f08000e;
        public static final int action_divider = 0x7f080010;
        public static final int action_image = 0x7f080011;
        public static final int action_text = 0x7f080017;
        public static final int actions = 0x7f080019;
        public static final int async = 0x7f080020;
        public static final int blocking = 0x7f080023;
        public static final int chronometer = 0x7f08002c;
        public static final int forever = 0x7f080054;
        public static final int icon = 0x7f080059;
        public static final int icon_group = 0x7f08005a;
        public static final int info = 0x7f08005d;
        public static final int italic = 0x7f08005e;
        public static final int line1 = 0x7f080063;
        public static final int line3 = 0x7f080064;
        public static final int normal = 0x7f080072;
        public static final int notification_background = 0x7f080073;
        public static final int notification_main_column = 0x7f080074;
        public static final int notification_main_column_container = 0x7f080075;
        public static final int right_icon = 0x7f080086;
        public static final int right_side = 0x7f080087;
        public static final int tag_transition_group = 0x7f0800bf;
        public static final int tag_unhandled_key_event_manager = 0x7f0800c0;
        public static final int tag_unhandled_key_listeners = 0x7f0800c1;
        public static final int text = 0x7f0800c2;
        public static final int text2 = 0x7f0800c3;
        public static final int time = 0x7f0800cb;
        public static final int title = 0x7f0800cc;
    }
    public static final class integer {
        private integer() {}

        public static final int status_bar_notification_info_maxnum = 0x7f09000e;
    }
    public static final class layout {
        private layout() {}

        public static final int notification_action = 0x7f0b0037;
        public static final int notification_action_tombstone = 0x7f0b0038;
        public static final int notification_template_custom_big = 0x7f0b0039;
        public static final int notification_template_icon_group = 0x7f0b003a;
        public static final int notification_template_part_chronometer = 0x7f0b003b;
        public static final int notification_template_part_time = 0x7f0b003c;
    }
    public static final class string {
        private string() {}

        public static final int status_bar_notification_info_overflow = 0x7f0f004d;
    }
    public static final class style {
        private style() {}

        public static final int TextAppearance_Compat_Notification = 0x7f10013a;
        public static final int TextAppearance_Compat_Notification_Info = 0x7f10013b;
        public static final int TextAppearance_Compat_Notification_Line2 = 0x7f10013c;
        public static final int TextAppearance_Compat_Notification_Time = 0x7f10013d;
        public static final int TextAppearance_Compat_Notification_Title = 0x7f10013e;
        public static final int Widget_Compat_NotificationActionContainer = 0x7f1001f4;
        public static final int Widget_Compat_NotificationActionText = 0x7f1001f5;
    }
    public static final class styleable {
        private styleable() {}

        public static final int[] ColorStateListItem = { 0x10101a5, 0x101031f, 0x7f03002b };
        public static final int ColorStateListItem_android_color = 0;
        public static final int ColorStateListItem_android_alpha = 1;
        public static final int ColorStateListItem_alpha = 2;
        public static final int[] FontFamily = { 0x7f030102, 0x7f030103, 0x7f030104, 0x7f030105, 0x7f030106, 0x7f030107 };
        public static final int FontFamily_fontProviderAuthority = 0;
        public static final int FontFamily_fontProviderCerts = 1;
        public static final int FontFamily_fontProviderFetchStrategy = 2;
        public static final int FontFamily_fontProviderFetchTimeout = 3;
        public static final int FontFamily_fontProviderPackage = 4;
        public static final int FontFamily_fontProviderQuery = 5;
        public static final int[] FontFamilyFont = { 0x1010532, 0x1010533, 0x101053f, 0x101056f, 0x1010570, 0x7f030100, 0x7f030108, 0x7f030109, 0x7f03010a, 0x7f03020f };
        public static final int FontFamilyFont_android_font = 0;
        public static final int FontFamilyFont_android_fontWeight = 1;
        public static final int FontFamilyFont_android_fontStyle = 2;
        public static final int FontFamilyFont_android_ttcIndex = 3;
        public static final int FontFamilyFont_android_fontVariationSettings = 4;
        public static final int FontFamilyFont_font = 5;
        public static final int FontFamilyFont_fontStyle = 6;
        public static final int FontFamilyFont_fontVariationSettings = 7;
        public static final int FontFamilyFont_fontWeight = 8;
        public static final int FontFamilyFont_ttcIndex = 9;
        public static final int[] GradientColor = { 0x101019d, 0x101019e, 0x10101a1, 0x10101a2, 0x10101a3, 0x10101a4, 0x1010201, 0x101020b, 0x1010510, 0x1010511, 0x1010512, 0x1010513 };
        public static final int GradientColor_android_startColor = 0;
        public static final int GradientColor_android_endColor = 1;
        public static final int GradientColor_android_type = 2;
        public static final int GradientColor_android_centerX = 3;
        public static final int GradientColor_android_centerY = 4;
        public static final int GradientColor_android_gradientRadius = 5;
        public static final int GradientColor_android_tileMode = 6;
        public static final int GradientColor_android_centerColor = 7;
        public static final int GradientColor_android_startX = 8;
        public static final int GradientColor_android_startY = 9;
        public static final int GradientColor_android_endX = 10;
        public static final int GradientColor_android_endY = 11;
        public static final int[] GradientColorItem = { 0x10101a5, 0x1010514 };
        public static final int GradientColorItem_android_color = 0;
        public static final int GradientColorItem_android_offset = 1;
    }
}
