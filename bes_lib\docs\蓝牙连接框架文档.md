# BES蓝牙连接框架文档

## 概述

BES蓝牙连接框架是一个基于Android平台的经典蓝牙通信解决方案，主要用于建立SPP（Serial Port Profile）连接，实现音频数据流传输和设备控制。该框架采用模块化设计，提供了完整的蓝牙设备扫描、连接管理、数据传输和消息解析功能。

### 核心特性

- **SPP协议支持**：基于串口协议进行蓝牙通信
- **音频数据流传输**：支持实时音频数据的双向传输
- **设备扫描**：支持经典蓝牙和BLE设备发现
- **连接管理**：自动重连、状态监控、异常处理
- **消息解析**：智能数据包解析和协议处理
- **线程安全**：多线程环境下的安全操作

## 架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│                    回调接口层 (Callback Layer)               │
│  ┌─────────────────┐    ┌─────────────────────────────────┐  │
│  │  ConnectCallback │    │        ScanCallback           │  │
│  └─────────────────┘    └─────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Business Layer)               │
│  ┌─────────────────┐    ┌─────────────────────────────────┐  │
│  │   SppConnector  │    │      Scanner系列               │  │
│  │                 │    │  ┌─────────────────────────────┐ │  │
│  │                 │    │  │     ClassicScanner        │ │  │
│  │                 │    │  │     LeScannerCompat       │ │  │
│  │                 │    │  └─────────────────────────────┘ │  │
│  └─────────────────┘    └─────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    工具层 (Utility Layer)                   │
│  ┌─────────────────┐    ┌─────────────────────────────────┐  │
│  │    BtHelper     │    │    SppMessageHelper           │  │
│  └─────────────────┘    └─────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                Android蓝牙API (Android Bluetooth API)       │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. SppConnector - SPP连接器

**职责**：
- 管理蓝牙SPP连接的生命周期
- 处理连接状态变化
- 提供数据读写接口
- 维护连接状态和回调管理

**关键特性**：
- 单例模式设计，全局唯一连接实例
- 支持异步连接建立
- 自动状态管理（连接中、已连接、已断开）
- 线程安全的回调机制

#### 2. BtHelper - 蓝牙工具类

**职责**：
- 提供蓝牙适配器获取方法
- 处理设备配对和解绑操作
- 解析蓝牙广播数据
- 兼容不同Android版本的API差异

#### 3. Scanner系列 - 设备扫描器

**职责**：
- 扫描经典蓝牙和BLE设备
- 处理扫描结果回调
- 管理扫描状态

**组件结构**：
- `BaseScanner`：扫描器基类，定义通用接口
- `ClassicScanner`：经典蓝牙扫描器
- `LeScannerCompat`：BLE扫描器兼容类

#### 4. SppMessageHelper - 消息处理器

**职责**：
- 解析SPP数据包
- 处理数据流拼接
- 识别音频数据和控制指令
- 管理数据缓存

## 连接流程详解

### 1. 设备扫描阶段

```mermaid
sequenceDiagram
    participant App as 应用层
    participant Scanner as ClassicScanner
    participant Adapter as BluetoothAdapter
    participant Receiver as BroadcastReceiver

    App->>Scanner: startScan(callback)
    Scanner->>Adapter: startDiscovery()
    Adapter->>Receiver: ACTION_DISCOVERY_STARTED
    Receiver->>Scanner: onScanStart()
    Scanner->>App: callback.onScanStart()
    
    loop 设备发现
        Adapter->>Receiver: ACTION_FOUND
        Receiver->>Scanner: onFound(device, rssi)
        Scanner->>App: callback.onFound(device, rssi)
    end
    
    Adapter->>Receiver: ACTION_DISCOVERY_FINISHED
    Receiver->>Scanner: onScanFinish()
    Scanner->>App: callback.onScanFinish()
```

### 2. 连接建立阶段

```mermaid
sequenceDiagram
    participant App as 应用层
    participant Connector as SppConnector
    participant Socket as BluetoothSocket
    participant Thread as ConnectRunnable

    App->>Connector: connect(device)
    Connector->>Thread: new ConnectRunnable(device)
    Thread->>Socket: createInsecureRfcommSocketToServiceRecord(UUID)
    Thread->>Socket: connect()
    
    alt 连接成功
        Thread->>Connector: onConnectionStateChanged(true)
        Connector->>App: callback.onConnectionStateChanged(true)
        Thread->>Thread: 启动ConnectedRunnable
    else 连接失败
        Thread->>Connector: onConnectionStateChanged(false)
        Connector->>App: callback.onConnectionStateChanged(false)
    end
```

### 3. 数据传输阶段

```mermaid
sequenceDiagram
    participant App as 应用层
    participant Connector as SppConnector
    participant Connected as ConnectedRunnable
    participant Helper as SppMessageHelper

    loop 数据接收
        Connected->>Connected: inputStream.read(data)
        Connected->>Helper: checkDataEnoughAndRetArray(data)
        Helper->>Helper: 解析数据包
        Helper-->>Connected: 返回完整数据包
        Connected->>Connector: onReceive(data)
        Connector->>App: callback.onReceive(data)
    end
    
    App->>Connector: write(data)
    Connector->>Connected: write(data)
    Connected->>Connected: outputStream.write(data)
```

## 数据传输协议

### SPP数据包格式

框架支持两种类型的数据：

#### 1. 音频数据流
```
格式：0xFF 0xFF [音频数据]
说明：以0xFF 0xFF开头的数据包被识别为音频流数据
```

#### 2. 控制指令
```
指令类型及格式：
- 0x07: 控制指令 (5字节)
- 0x04: 开始数据流标志 (5字节)  
- 0x05: 结束数据流标志 (4字节)
- 0x01: 数据流开始标志 (12字节)
- 0x03: 数据流结束标志 (16字节)
- 0x00: 可变长度指令 (长度由第3-4字节指定)
```

### 数据包解析流程

1. **数据接收**：从InputStream读取原始字节数据
2. **数据拼接**：将新数据与缓存数据合并
3. **协议识别**：根据包头识别数据类型
4. **长度校验**：验证数据包完整性
5. **数据提取**：提取完整的数据包
6. **剩余处理**：处理未完成的数据包

## 使用示例

### 基本连接流程

```java
// 1. 获取连接器实例
SppConnector connector = SppConnector.getConnector();

// 2. 设置连接回调
connector.addConnectCallback(new ConnectCallback() {
    @Override
    public void onConnectionStateChanged(boolean connected) {
        if (connected) {
            Log.d(TAG, "蓝牙连接成功");
        } else {
            Log.d(TAG, "蓝牙连接断开");
        }
    }

    @Override
    public void onReceive(UUID uuid, byte[] data) {
        Log.d(TAG, "接收到数据: " + ArrayUtil.toHex(data));
        // 处理接收到的数据
    }
});

// 3. 建立连接
String deviceAddress = "00:11:22:33:44:55";
connector.connect(deviceAddress);

// 4. 发送数据
byte[] data = {0x01, 0x02, 0x03};
connector.write(data);

// 5. 断开连接
connector.disconnect();
```

### 设备扫描示例

```java
// 1. 创建扫描器
ClassicScanner scanner = new ClassicScanner(context);

// 2. 设置扫描回调
scanner.startScan(new ScanCallback() {
    @Override
    public void onScanStart() {
        Log.d(TAG, "开始扫描设备");
    }

    @Override
    public void onFound(BluetoothDevice device, int rssi, byte[] scanRecord) {
        Log.d(TAG, "发现设备: " + device.getName() + " - " + device.getAddress());
    }

    @Override
    public void onScanFinish() {
        Log.d(TAG, "扫描完成");
    }
});

// 3. 停止扫描
scanner.stopScan();

// 4. 释放资源
scanner.close();
```

## 状态管理

### 连接状态

框架定义了三种连接状态：

```java
private static final int STATE_CONNECTING = 1;    // 连接中
private static final int STATE_CONNECTED = 2;     // 已连接
private static final int STATE_DISCONNECTED = 0;  // 已断开
```

### 状态转换图

```mermaid
stateDiagram-v2
    [*] --> DISCONNECTED
    DISCONNECTED --> CONNECTING: connect()
    CONNECTING --> CONNECTED: 连接成功
    CONNECTING --> DISCONNECTED: 连接失败
    CONNECTED --> DISCONNECTED: disconnect() / 连接异常
    DISCONNECTED --> [*]
```

### 状态监控

```java
public boolean isConnected() {
    return mConnState == STATE_CONNECTED;
}
```

## 错误处理机制

### 连接异常处理

1. **连接超时**：在ConnectRunnable中捕获IOException
2. **设备不可达**：检查设备是否在范围内
3. **权限不足**：确保应用具有蓝牙权限
4. **适配器未启用**：检查蓝牙适配器状态

### 数据传输异常

1. **写入失败**：在ConnectedRunnable.write()中处理IOException
2. **读取中断**：在数据读取循环中处理异常
3. **数据包不完整**：通过SppMessageHelper进行数据缓存和拼接

### 异常恢复策略

```java
// 连接异常时自动触发断开回调
catch (IOException e) {
    e.printStackTrace();
    onConnectionStateChanged(false);
}
```

## 线程模型

### 线程架构

```
主线程 (UI Thread)
├── 回调通知
└── 状态更新

工作线程 (Worker Threads)
├── ConnectRunnable (连接线程)
│   └── 负责建立蓝牙连接
└── ConnectedRunnable (数据传输线程)
    ├── 数据读取循环
    └── 数据写入操作
```

### 线程安全

- 使用`synchronized`关键字保护回调列表
- 通过`volatile`修饰符确保状态变量的可见性
- 采用线程安全的单例模式

## 内存管理

### 资源释放

1. **Socket关闭**：在disconnect()方法中关闭BluetoothSocket
2. **流关闭**：在ConnectedRunnable的finally块中关闭InputStream
3. **回调清理**：提供removeAllCallBack()方法清理回调
4. **数据缓存清理**：SppMessageHelper.clearSppData()清理缓存

### 内存优化

- 使用对象池复用字节数组
- 及时释放不再使用的资源
- 避免内存泄漏的回调引用

## 配置参数

### UUID配置

```java
// SPP服务UUID (Serial Port Profile)
public static final UUID sUUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
```

### 缓冲区配置

```java
// 数据读取缓冲区大小
byte[] data = new byte[1944];
```

### 超时配置

框架使用Android系统默认的蓝牙连接超时时间，通常为10-12秒。

## 最佳实践

### 1. 权限管理

确保在AndroidManifest.xml中声明必要权限：

```xml
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
```

### 2. 生命周期管理

```java
@Override
protected void onDestroy() {
    super.onDestroy();
    // 清理连接资源
    SppConnector.getConnector().removeAllCallBack();
    SppConnector.getConnector().disconnect();

    // 清理扫描资源
    if (scanner != null) {
        scanner.close();
    }
}
```

### 3. 异常处理

```java
// 连接前检查蓝牙状态
BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
if (adapter == null || !adapter.isEnabled()) {
    // 处理蓝牙不可用情况
    return;
}

// 连接前检查设备有效性
BluetoothDevice device = adapter.getRemoteDevice(address);
if (device == null) {
    // 处理设备地址无效情况
    return;
}
```

### 4. 性能优化

- 在不需要时及时停止设备扫描
- 合理设置数据读取缓冲区大小
- 避免在主线程进行蓝牙操作
- 使用连接池管理多设备连接

### 5. 调试建议

- 启用详细的日志输出
- 监控连接状态变化
- 记录数据传输统计信息
- 使用蓝牙调试工具验证协议

## 常见问题

### Q1: 连接失败怎么办？

**A**: 检查以下几点：
1. 设备是否在蓝牙范围内
2. 目标设备是否支持SPP协议
3. 是否已正确配对设备
4. 应用是否具有必要的蓝牙权限

### Q2: 数据传输不稳定？

**A**: 可能的原因和解决方案：
1. 信号干扰：减少环境中的2.4GHz设备干扰
2. 距离过远：保持设备在有效通信范围内
3. 数据包过大：分片传输大数据包
4. 传输频率过高：控制数据发送频率

### Q3: 内存泄漏问题？

**A**: 注意以下几点：
1. 及时移除不再使用的回调
2. 在Activity销毁时断开蓝牙连接
3. 正确关闭输入输出流
4. 清理SppMessageHelper中的缓存数据

### Q4: 多设备连接支持？

**A**: 当前框架采用单例模式，仅支持单一设备连接。如需多设备连接，需要：
1. 修改SppConnector为非单例模式
2. 为每个设备创建独立的连接实例
3. 管理多个连接的状态和回调

## 扩展开发

### 自定义协议支持

可以通过扩展SppMessageHelper来支持自定义协议：

```java
public class CustomMessageHelper extends SppMessageHelper {
    @Override
    public byte[][] checkDataEnoughAndRetArray(byte[] data) {
        // 实现自定义协议解析逻辑
        return super.checkDataEnoughAndRetArray(data);
    }
}
```

### 连接策略扩展

可以实现自定义的连接策略：

```java
public class AutoReconnectConnector extends SppConnector {
    private static final int MAX_RETRY_COUNT = 3;
    private int retryCount = 0;

    @Override
    protected void onConnectionStateChanged(boolean connected) {
        if (!connected && retryCount < MAX_RETRY_COUNT) {
            // 实现自动重连逻辑
            retryCount++;
            // 延迟重连
        }
        super.onConnectionStateChanged(connected);
    }
}
```

## 版本历史

- **v1.0.0** (2017/12/11): 初始版本，支持基本的SPP连接功能
- **v1.1.0**: 添加设备扫描功能
- **v1.2.0**: 优化数据传输性能
- **v1.3.0**: 增强错误处理机制

## 技术支持

如有技术问题或建议，请联系开发团队：
- 邮箱：<EMAIL>
- 文档更新：请查看项目Git仓库
- 问题反馈：通过Issue系统提交
